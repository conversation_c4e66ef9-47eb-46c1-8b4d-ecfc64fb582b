import 'package:flutter/material.dart';

class GalleryScreen extends StatelessWidget {
  const GalleryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('View Gallery'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Gallery Categories
            SizedBox(
              height: 50,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  _buildCategoryChip('All', true),
                  _buildCategoryChip('Ghats', false),
                  _buildCategoryChip('Rituals', false),
                  _buildCategoryChip('Cultural', false),
                  _buildCategoryChip('Aerial View', false),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Gallery Grid
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 1,
                ),
                itemCount: 12,
                itemBuilder: (context, index) {
                  return _buildGalleryItem(context, index);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryChip(String label, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (bool selected) {
          // Handle category selection
        },
        selectedColor: const Color(0xFF4CAF50).withValues(alpha: 0.2),
        checkmarkColor: const Color(0xFF4CAF50),
      ),
    );
  }

  Widget _buildGalleryItem(BuildContext context, int index) {
    final List<String> titles = [
      'Kaleshwaram Ghat',
      'Morning Rituals',
      'Cultural Dance',
      'Aerial View',
      'Devotees Gathering',
      'Sacred Fire',
      'River View',
      'Temple Complex',
      'Evening Aarti',
      'Boat Services',
      'Food Stalls',
      'Accommodation',
    ];

    final List<IconData> icons = [
      Icons.water,
      Icons.wb_sunny,
      Icons.music_note,
      Icons.flight,
      Icons.groups,
      Icons.local_fire_department,
      Icons.waves,
      Icons.temple_hindu,
      Icons.nights_stay,
      Icons.directions_boat,
      Icons.restaurant,
      Icons.hotel,
    ];

    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () => _showImageDialog(context, titles[index]),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.blue[300]!,
                Colors.blue[600]!,
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icons[index],
                size: 40,
                color: Colors.white,
              ),
              const SizedBox(height: 8),
              Text(
                titles[index],
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showImageDialog(BuildContext context, String title) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Container(
            width: 300,
            height: 200,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              gradient: LinearGradient(
                colors: [Colors.blue[300]!, Colors.blue[600]!],
              ),
            ),
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.photo,
                    size: 60,
                    color: Colors.white,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Image Preview',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'High-quality images will be\navailable in the actual app',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Image downloaded')),
                );
              },
              child: const Text('Download'),
            ),
          ],
        );
      },
    );
  }
}
