<!DOCTYPE html>
<html>
<head>
  <base href="/">
  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Godavari Pushkaralu 2027 Official App">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Godavari Pushkaralu">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>Godavari Pushkaralu 2027</title>
  <link rel="manifest" href="manifest.json">

  <script>
    // The value below is injected by flutter build, do not touch.
    const serviceWorkerVersion = "2882689468";
  </script>

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-auth.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-messaging.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-analytics.js"></script>

  <script>
    // Your web app's Firebase configuration
    const firebaseConfig = {
      // TODO: Replace with your Firebase config
      apiKey: "your-api-key",
      authDomain: "your-auth-domain",
      projectId: "your-project-id",
      storageBucket: "your-storage-bucket",
      messagingSenderId: "your-messaging-sender-id",
      appId: "your-app-id",
      measurementId: "your-measurement-id"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
  </script>

  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js" defer></script>
</head>
<body>
  <script>
    window.addEventListener('load', function(ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            appRunner.runApp();
          });
        }
      });
    });
  </script>
</body>
</html>
