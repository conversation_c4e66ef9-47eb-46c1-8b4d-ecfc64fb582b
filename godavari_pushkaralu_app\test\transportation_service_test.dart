import 'package:flutter_test/flutter_test.dart';
import 'package:godavari_pushkaralu_app/services/transportation_service.dart';

void main() {
  group('TransportationService Tests', () {
    TransportationService createService() {
      final service = TransportationService.newInstance();
      service.initialize();
      return service;
    }

    test('should initialize with vehicles, parking, and traffic data', () {
      final service = createService();
      expect(service.vehicles.isNotEmpty, true);
      expect(service.parkingSpots.isNotEmpty, true);
      expect(service.trafficUpdates.isNotEmpty, true);
      service.dispose();
    });

    test('should have different vehicle types', () {
      final service = createService();
      final vehicles = service.vehicles;
      
      final buses = vehicles.where((v) => v.type == TransportType.bus).toList();
      final taxis = vehicles.where((v) => v.type == TransportType.taxi).toList();
      final autos = vehicles.where((v) => v.type == TransportType.autoRickshaw).toList();
      final boats = vehicles.where((v) => v.type == TransportType.boat).toList();
      final shuttles = vehicles.where((v) => v.type == TransportType.shuttle).toList();

      expect(buses.isNotEmpty, true);
      expect(taxis.isNotEmpty, true);
      expect(autos.isNotEmpty, true);
      expect(boats.isNotEmpty, true);
      expect(shuttles.isNotEmpty, true);
      service.dispose();
    });

    test('should filter vehicles by type', () {
      final service = createService();
      final buses = service.getVehiclesByType(TransportType.bus);
      expect(buses.every((v) => v.type == TransportType.bus), true);
      service.dispose();
    });

    test('should get available vehicles', () {
      final service = createService();
      final available = service.getAvailableVehicles();
      expect(available.every((v) => v.status == VehicleStatus.available), true);
      service.dispose();
    });

    test('should get available parking spots', () {
      final service = createService();
      final available = service.getAvailableParking();
      expect(available.every((spot) => spot.availableSpots > 0 && spot.isOpen), true);
      service.dispose();
    });

    test('should simulate vehicle booking', () async {
      final service = createService();
      final vehicle = service.getAvailableVehicles().first;
      final initialPassengers = vehicle.currentPassengers;

      final request = BookingRequest(
        vehicleId: vehicle.id,
        pickupLocation: 'Test Pickup',
        dropLocation: 'Test Drop',
        requestTime: DateTime.now(),
        passengers: 1,
        customerName: 'Test User',
        customerPhone: '+91-9876543210',
      );

      final bookingId = await service.bookVehicle(request);
      expect(bookingId.isNotEmpty, true);
      expect(bookingId.startsWith('BK'), true);

      // Check if passengers were updated
      final updatedVehicle = service.vehicles.firstWhere((v) => v.id == vehicle.id);
      expect(updatedVehicle.currentPassengers, initialPassengers + 1);
      service.dispose();
    });

    test('should handle booking when vehicle is full', () async {
      final service = createService();
      final vehicle = service.vehicles.first;
      
      // Create a booking request that exceeds capacity
      final request = BookingRequest(
        vehicleId: vehicle.id,
        pickupLocation: 'Test Pickup',
        dropLocation: 'Test Drop',
        requestTime: DateTime.now(),
        passengers: vehicle.capacity + 1,
        customerName: 'Test User',
        customerPhone: '+91-9876543210',
      );

      expect(
        () async => await service.bookVehicle(request),
        throwsException,
      );
      service.dispose();
    });

    test('should have real-time vehicle updates stream', () async {
      final service = createService();

      // The stream should emit data immediately after initialization
      final vehicles = await service.vehiclesStream.first;
      expect(vehicles.isNotEmpty, true);
      service.dispose();
    });

    test('should have real-time parking updates stream', () async {
      final service = createService();

      // The stream should emit data immediately after initialization
      final parking = await service.parkingStream.first;
      expect(parking.isNotEmpty, true);
      service.dispose();
    });

    test('should have real-time traffic updates stream', () async {
      final service = createService();

      // The stream should emit data immediately after initialization
      final traffic = await service.trafficStream.first;
      expect(traffic.isNotEmpty, true);
      service.dispose();
    });

    test('should have different traffic levels', () {
      final service = createService();
      final traffic = service.trafficUpdates;
      
      final hasLight = traffic.any((t) => t.trafficLevel == TrafficLevel.light);
      final hasModerate = traffic.any((t) => t.trafficLevel == TrafficLevel.moderate);
      final hasHeavy = traffic.any((t) => t.trafficLevel == TrafficLevel.heavy);
      
      expect(hasLight || hasModerate || hasHeavy, true);
      service.dispose();
    });

    test('should have parking spots with different capacities', () {
      final service = createService();
      final parking = service.parkingSpots;
      
      expect(parking.any((spot) => spot.totalSpots > 50), true);
      expect(parking.any((spot) => spot.hourlyRate > 0), true);
      service.dispose();
    });

    test('should have vehicles with valid data', () {
      final service = createService();
      final vehicles = service.vehicles;

      // Check that vehicles have valid data
      expect(vehicles.every((v) => v.id.isNotEmpty), true);
      expect(vehicles.every((v) => v.number.isNotEmpty), true);
      expect(vehicles.every((v) => v.driverName.isNotEmpty), true);
      expect(vehicles.every((v) => v.capacity > 0), true);
      expect(vehicles.every((v) => v.fare > 0), true);
      service.dispose();
    });
  });
}
