import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'animated_sidebar.dart';

class MainLayout extends StatefulWidget {
  final Widget child;
  final String currentRoute;

  const MainLayout({
    super.key,
    required this.child,
    required this.currentRoute,
  });

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  bool _isSidebarOpen = false;

  void _toggleSidebar() {
    setState(() {
      _isSidebarOpen = !_isSidebarOpen;
    });
  }

  void _closeSidebar() {
    setState(() {
      _isSidebarOpen = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Main content
          widget.child,

          // Sidebar overlay
          if (_isSidebarOpen)
            AnimatedSidebar(
              currentRoute: widget.currentRoute,
              onClose: _closeSidebar,
            ),

          // Floating sidebar button
          _buildSidebarFAB(),
        ],
      ),
    );
  }

  Widget _buildSidebarFAB() {
    return Positioned(
      top: 50,
      left: 16,
      child: Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFF1976D2), Color(0xFF42A5F5)],
          ),
          borderRadius: BorderRadius.circular(28),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(28),
            onTap: _toggleSidebar,
            child: Container(
              width: 56,
              height: 56,
              padding: const EdgeInsets.all(8),
              child: Stack(
                children: [
                  Center(
                    child: AnimatedRotation(
                      turns: _isSidebarOpen ? 0.5 : 0.0,
                      duration: const Duration(milliseconds: 300),
                      child: Icon(
                        _isSidebarOpen ? Icons.close : Icons.menu,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  if (!_isSidebarOpen)
                    const Positioned(
                      top: 2,
                      right: 2,
                      child: Text(
                        'ॐ',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Extension to easily wrap screens with MainLayout
extension MainLayoutExtension on Widget {
  Widget withMainLayout(String currentRoute) {
    return MainLayout(
      currentRoute: currentRoute,
      child: this,
    );
  }
}
