import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class MoreScreen extends StatelessWidget {
  const MoreScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('More Options'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Services Section
            _buildSectionHeader('Services'),
            _buildMenuItem(
              'Lost & Found',
              'Report or find lost items',
              Icons.search,
              () => context.go('/lost-found'),
            ),
            _buildMenuItem(
              'Medical Services',
              'Find nearby hospitals and clinics',
              Icons.medical_services,
              () => context.go('/medical'),
            ),
            _buildMenuItem(
              'Parking Information',
              'Find parking areas and rates',
              Icons.local_parking,
              () => context.go('/parking'),
            ),
            _buildMenuItem(
              'Toilets & Facilities',
              'Locate public facilities',
              Icons.wc,
              () => context.go('/facilities'),
            ),
            
            const SizedBox(height: 20),
            
            // Information Section
            _buildSectionHeader('Information'),
            _buildMenuItem(
              'Event Schedule',
              'Complete program schedule',
              Icons.schedule,
              () => context.go('/schedule'),
            ),
            _buildMenuItem(
              'Transportation',
              'Bus, train, and flight info',
              Icons.directions_bus,
              () => context.go('/transportation'),
            ),
            _buildMenuItem(
              'Weather Updates',
              'Current weather conditions',
              Icons.wb_sunny,
              () => context.go('/weather'),
            ),
            _buildMenuItem(
              'Language Settings',
              'Change app language',
              Icons.language,
              () => _showLanguageDialog(context),
            ),
            
            const SizedBox(height: 20),
            
            // Support Section
            _buildSectionHeader('Support'),
            _buildMenuItem(
              'Contact Us',
              'Get in touch with support',
              Icons.contact_support,
              () => context.go('/contact'),
            ),
            _buildMenuItem(
              'Feedback',
              'Share your experience',
              Icons.feedback,
              () => context.go('/feedback'),
            ),
            _buildMenuItem(
              'FAQ',
              'Frequently asked questions',
              Icons.help_outline,
              () => context.go('/faq'),
            ),
            _buildMenuItem(
              'Privacy Policy',
              'Read our privacy policy',
              Icons.privacy_tip,
              () => context.go('/privacy'),
            ),
            
            const SizedBox(height: 20),
            
            // App Information
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: const Column(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 40,
                    color: Color(0xFF4CAF50),
                  ),
                  SizedBox(height: 12),
                  Text(
                    'Godavari Pushkaralu 2027',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    'Version 1.0.0',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Developed by GP InfoTech',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12, top: 8),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ),
    );
  }

  Widget _buildMenuItem(String title, String subtitle, IconData icon, VoidCallback onTap) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: const Color(0xFF4CAF50),
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey,
        ),
        onTap: onTap,
      ),
    );
  }

  void _showLanguageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Language'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('English'),
                leading: const Icon(Icons.language),
                onTap: () {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Language set to English')),
                  );
                },
              ),
              ListTile(
                title: const Text('తెలుగు'),
                leading: const Icon(Icons.language),
                onTap: () {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Language set to Telugu')),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
