import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:godavari_pushkaralu_app/screens/splash_screen.dart';
import 'package:godavari_pushkaralu_app/screens/home_screen.dart';
import 'package:godavari_pushkaralu_app/screens/auth/login_screen.dart';
import 'package:godavari_pushkaralu_app/screens/ghats/ghats_screen.dart';
import 'package:godavari_pushkaralu_app/screens/schedule/schedule_screen.dart';
import 'package:godavari_pushkaralu_app/screens/premium/premium_screen.dart';

class NavigationService {
  static final router = GoRouter(
    initialLocation: '/',
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/home',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/ghats',
        builder: (context, state) => const GhatsScreen(),
      ),
      GoRoute(
        path: '/schedule',
        builder: (context, state) => const ScheduleScreen(),
      ),
      GoRoute(
        path: '/premium',
        builder: (context, state) => const PremiumScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Text(
          'Page not found!',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
      ),
    ),
  );
} 