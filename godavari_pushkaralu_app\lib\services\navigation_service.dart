import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:godavari_pushkaralu_app/screens/splash_screen.dart';
import 'package:godavari_pushkaralu_app/screens/home_screen.dart';
import 'package:godavari_pushkaralu_app/screens/auth/login_screen.dart';
import 'package:godavari_pushkaralu_app/screens/ghats/ghats_screen.dart';
import 'package:godavari_pushkaralu_app/screens/schedule/schedule_screen.dart';
import 'package:godavari_pushkaralu_app/screens/premium/premium_screen.dart';
import 'package:godavari_pushkaralu_app/screens/onboarding_screen.dart';
import 'package:godavari_pushkaralu_app/screens/profile_screen.dart';
import 'package:godavari_pushkaralu_app/screens/emergency_screen.dart';
import 'package:godavari_pushkaralu_app/screens/community_screen.dart';
import 'package:godavari_pushkaralu_app/screens/stay_food_screen.dart';
import 'package:godavari_pushkaralu_app/screens/transportation_screen.dart';
import 'package:godavari_pushkaralu_app/screens/medical_services_screen.dart';
import 'package:godavari_pushkaralu_app/screens/weather_forecast_screen.dart';
import 'package:godavari_pushkaralu_app/screens/services_screen.dart';
import 'package:godavari_pushkaralu_app/screens/live_queue_screen.dart';
import 'package:godavari_pushkaralu_app/screens/maps_screen.dart';
import 'package:godavari_pushkaralu_app/screens/emergency_screen.dart';
import 'package:godavari_pushkaralu_app/widgets/main_layout.dart';

class NavigationService {
  static final router = GoRouter(
    initialLocation: '/onboarding',
    routes: [
      GoRoute(
        path: '/onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),
      GoRoute(
        path: '/',
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/home',
        builder: (context, state) => const HomeScreen().withMainLayout('/home'),
      ),
      GoRoute(
        path: '/explore',
        builder: (context, state) => _buildPlaceholderScreen('Explore'),
      ),
      GoRoute(
        path: '/sos',
        builder: (context, state) => _buildPlaceholderScreen('Emergency SOS'),
      ),
      GoRoute(
        path: '/about',
        builder: (context, state) => _buildPlaceholderScreen('About'),
      ),
      GoRoute(
        path: '/more',
        builder: (context, state) => _buildPlaceholderScreen('More Options'),
      ),
      GoRoute(
        path: '/gallery',
        builder: (context, state) => _buildPlaceholderScreen('Gallery'),
      ),
      GoRoute(
        path: '/attractions',
        builder: (context, state) => _buildPlaceholderScreen('Attractions'),
      ),
      GoRoute(
        path: '/accommodation',
        builder: (context, state) => _buildPlaceholderScreen('Accommodation'),
      ),
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/ghats',
        builder: (context, state) => const GhatsScreen(),
      ),
      GoRoute(
        path: '/schedule',
        builder: (context, state) => const ScheduleScreen().withMainLayout('/schedule'),
      ),
      GoRoute(
        path: '/premium',
        builder: (context, state) => const PremiumScreen(),
      ),
      GoRoute(
        path: '/lost-found',
        builder: (context, state) => _buildPlaceholderScreen('Lost & Found'),
      ),
      GoRoute(
        path: '/events',
        builder: (context, state) => _buildPlaceholderScreen('Events'),
      ),

      // Placeholder routes for other features
      GoRoute(
        path: '/hanuman',
        builder: (context, state) => _buildPlaceholderScreen('Hanuman Chalisa'),
      ),
      GoRoute(
        path: '/marathi',
        builder: (context, state) => _buildPlaceholderScreen('Marathi Content'),
      ),
      GoRoute(
        path: '/spiritual-visit',
        builder: (context, state) => _buildPlaceholderScreen('Spiritual Visit Guide'),
      ),
      GoRoute(
        path: '/spiritual-ghats',
        builder: (context, state) => _buildPlaceholderScreen('Spiritual Ghats'),
      ),
      GoRoute(
        path: '/prasadam',
        builder: (context, state) => _buildPlaceholderScreen('Prasadam Information'),
      ),
      GoRoute(
        path: '/language-settings',
        builder: (context, state) => _buildPlaceholderScreen('Language Settings'),
      ),
      GoRoute(
        path: '/parking',
        builder: (context, state) => _buildPlaceholderScreen('Parking Information'),
      ),
      GoRoute(
        path: '/medical',
        builder: (context, state) => const MedicalServicesScreen().withMainLayout('/medical'),
      ),
      GoRoute(
        path: '/facilities',
        builder: (context, state) => _buildPlaceholderScreen('Public Facilities'),
      ),

      GoRoute(
        path: '/food',
        builder: (context, state) => _buildPlaceholderScreen('Food & Prasadam'),
      ),
      GoRoute(
        path: '/contact',
        builder: (context, state) => _buildPlaceholderScreen('Contact Us'),
      ),
      GoRoute(
        path: '/feedback',
        builder: (context, state) => _buildPlaceholderScreen('Feedback'),
      ),
      GoRoute(
        path: '/faq',
        builder: (context, state) => _buildPlaceholderScreen('FAQ'),
      ),
      GoRoute(
        path: '/privacy',
        builder: (context, state) => _buildPlaceholderScreen('Privacy Policy'),
      ),
      GoRoute(
        path: '/weather',
        builder: (context, state) => const WeatherForecastScreen().withMainLayout('/weather'),
      ),
      GoRoute(
        path: '/settings',
        builder: (context, state) => _buildPlaceholderScreen('Settings'),
      ),
      GoRoute(
        path: '/profile',
        builder: (context, state) => const ProfileScreen().withMainLayout('/profile'),
      ),
      GoRoute(
        path: '/community',
        builder: (context, state) => const CommunityScreen().withMainLayout('/community'),
      ),
      GoRoute(
        path: '/stay',
        builder: (context, state) => const StayFoodScreen().withMainLayout('/stay'),
      ),
      GoRoute(
        path: '/emergency',
        builder: (context, state) => const EmergencyScreen().withMainLayout('/emergency'),
      ),
      GoRoute(
        path: '/transportation',
        builder: (context, state) => const TransportationScreen().withMainLayout('/transportation'),
      ),
      GoRoute(
        path: '/services',
        builder: (context, state) => const ServicesScreen().withMainLayout('/services'),
      ),
      GoRoute(
        path: '/queue',
        builder: (context, state) => const LiveQueueScreen().withMainLayout('/queue'),
      ),
      GoRoute(
        path: '/maps',
        builder: (context, state) => const MapsScreen().withMainLayout('/maps'),
      ),
      GoRoute(
        path: '/emergency',
        builder: (context, state) => const EmergencyScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Text(
          'Page not found!',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
      ),
    ),
  );

  static Widget _buildPlaceholderScreen(String title) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.construction,
              size: 80,
              color: Colors.grey,
            ),
            const SizedBox(height: 20),
            Text(
              title,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              'This feature is coming soon!',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // Go back to previous screen
              },
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }
}