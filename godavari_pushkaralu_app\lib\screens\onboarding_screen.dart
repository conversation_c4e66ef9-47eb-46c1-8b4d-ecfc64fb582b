import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../constants/theme.dart';
import 'package:shimmer/shimmer.dart';
import 'package:godavari_pushkaralu_app/services/language_service.dart';
import 'package:godavari_pushkaralu_app/services/localization_service.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _controller = PageController();
  int _currentPage = 0;
  String _selectedLanguage = 'en';

  @override
  void initState() {
    super.initState();
    _selectedLanguage = languageService.currentLanguageCode;
    _checkOnboardingStatus();
  }

  void _checkOnboardingStatus() async {
    final isCompleted = await languageService.isOnboardingCompleted();
    if (isCompleted && mounted) {
      context.go('/home');
    }
  }

  IconData _getPageIcon(int index) {
    switch (index) {
      case 0: return Icons.language; // Language selection
      case 1: return Icons.temple_hindu; // Godavari Pushkaralu
      case 2: return Icons.self_improvement; // Spiritual journey
      case 3: return Icons.calendar_today; // Plan your visit
      case 4: return Icons.connect_without_contact; // Stay connected
      case 5: return Icons.star; // Premium experience
      default: return Icons.info;
    }
  }

  List<_OnboardPage> get _pages => [
    // Language Selection Page
    _OnboardPage(
      title: LocalizationService.t('choose_language', _selectedLanguage),
      description: LocalizationService.t('language_subtitle', _selectedLanguage),
      image: '',
      bgGradient: const LinearGradient(
        colors: [Color(0xFF0D47A1), Color(0xFF42A5F5), Color(0xFFFFD700)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      isLanguagePage: true,
    ),
    _OnboardPage(
      title: LocalizationService.t('godavari_pushkaralu', _selectedLanguage),
      description: LocalizationService.t('sacred_river_festival', _selectedLanguage),
      image: '',
      bgGradient: const LinearGradient(
        colors: [Color(0xFF0D47A1), Color(0xFF42A5F5), Color(0xFFFFD700)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    _OnboardPage(
      title: LocalizationService.t('spiritual_journey', _selectedLanguage),
      description: LocalizationService.t('spiritual_journey_desc', _selectedLanguage),
      image: '',
      bgGradient: const LinearGradient(
        colors: [Color(0xFF4A148C), Color(0xFF7B1FA2), Color(0xFFFFD700)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    _OnboardPage(
      title: LocalizationService.t('plan_your_visit', _selectedLanguage),
      description: LocalizationService.t('plan_visit_desc', _selectedLanguage),
      image: '',
      bgGradient: const LinearGradient(
        colors: [Color(0xFF1B5E20), Color(0xFF4CAF50), Color(0xFFFFD700)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    _OnboardPage(
      title: LocalizationService.t('stay_connected', _selectedLanguage),
      description: LocalizationService.t('stay_connected_desc', _selectedLanguage),
      image: '',
      bgGradient: const LinearGradient(
        colors: [Color(0xFFE65100), Color(0xFFFF9800), Color(0xFFFFD700)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    _OnboardPage(
      title: LocalizationService.t('premium_experience', _selectedLanguage),
      description: LocalizationService.t('premium_desc', _selectedLanguage),
      image: '',
      bgGradient: const LinearGradient(
        colors: [Color(0xFFBF360C), Color(0xFFFF5722), Color(0xFFFFD700)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
  ];

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onPageChanged(int idx) {
    setState(() => _currentPage = idx);
  }

  Widget _buildLanguageSelection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          // Beautiful Om symbol with glow effect
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.25),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.white.withValues(alpha: 0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: const Center(
              child: Text(
                'ॐ',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 50,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(color: Colors.black26, blurRadius: 8),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 40),
          Text(
            LocalizationService.t('choose_language', _selectedLanguage),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 32,
              fontWeight: FontWeight.bold,
              shadows: [Shadow(color: Colors.black26, blurRadius: 8)],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            LocalizationService.t('language_subtitle', _selectedLanguage),
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          // Improved language selection cards
          ...LanguageService.supportedLanguages.map((language) {
            final isSelected = _selectedLanguage == language['code'];
            return Container(
              margin: const EdgeInsets.symmetric(vertical: 10),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () async {
                    setState(() {
                      _selectedLanguage = language['code']!;
                    });
                    await languageService.changeLanguage(language['code']!);
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: isSelected
                        ? Colors.white.withValues(alpha: 0.35)
                        : Colors.white.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected ? Colors.white : Colors.white.withValues(alpha: 0.4),
                        width: isSelected ? 3 : 2,
                      ),
                      boxShadow: isSelected ? [
                        BoxShadow(
                          color: Colors.white.withValues(alpha: 0.3),
                          blurRadius: 15,
                          spreadRadius: 2,
                        ),
                      ] : [],
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Center(
                            child: Text(
                              language['flag']!,
                              style: const TextStyle(fontSize: 28),
                            ),
                          ),
                        ),
                        const SizedBox(width: 20),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                language['name']!,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  shadows: const [Shadow(color: Colors.black26, blurRadius: 4)],
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                language['nativeName']!,
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.85),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        AnimatedScale(
                          scale: isSelected ? 1.0 : 0.0,
                          duration: const Duration(milliseconds: 300),
                          child: Container(
                            width: 30,
                            height: 30,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.check,
                              color: Color(0xFF0D47A1),
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final page = _pages[_currentPage];
    return Scaffold(
      body: AnimatedContainer(
        duration: const Duration(milliseconds: 600),
        curve: Curves.easeInOut,
        decoration: BoxDecoration(
          gradient: page.bgGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: PageView.builder(
                  controller: _controller,
                  itemCount: _pages.length,
                  onPageChanged: _onPageChanged,
                  itemBuilder: (context, idx) {
                    final p = _pages[idx];
                    return AnimatedOpacity(
                      opacity: _currentPage == idx ? 1.0 : 0.0,
                      duration: const Duration(milliseconds: 500),
                      child: SingleChildScrollView(
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minHeight: MediaQuery.of(context).size.height,
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                          if (p.isLanguagePage) ...[
                            _buildLanguageSelection(),
                          ] else ...[
                            // Add tap hint for non-language pages
                            if (idx > 0) ...[
                              Container(
                                margin: const EdgeInsets.only(bottom: 20),
                                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  LocalizationService.t('tap_anywhere', _selectedLanguage),
                                  style: TextStyle(
                                    color: Colors.white.withValues(alpha: 0.8),
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                            Hero(
                              tag: 'onboard-img-$idx',
                              child: Container(
                                margin: const EdgeInsets.only(bottom: 32),
                                height: 140,
                                width: 140,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.25),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(alpha: 0.3),
                                      blurRadius: 30,
                                      offset: const Offset(0, 10),
                                    )
                                  ],
                                  borderRadius: BorderRadius.circular(35),
                                ),
                                child: Icon(
                                  _getPageIcon(idx),
                                  size: 70,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                            Text(
                              p.title,
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 32,
                                fontWeight: FontWeight.bold,
                                shadows: [Shadow(color: Colors.black26, blurRadius: 8)],
                              ),
                            ),
                            const SizedBox(height: 24),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 32.0),
                              child: Text(
                                p.description,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.95),
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                  height: 1.5,
                                ),
                              ),
                            ),
                          ],
                        ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(_pages.length, (idx) => AnimatedContainer(
                  duration: const Duration(milliseconds: 400),
                  margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 24),
                  width: _currentPage == idx ? 24 : 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: _currentPage == idx ? Colors.white : Colors.white54,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [if (_currentPage == idx) BoxShadow(color: Colors.black26, blurRadius: 6)],
                  ),
                )),
              ),
              // Navigation buttons with better visibility
              Container(
                color: Colors.black.withValues(alpha: 0.2),
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 20),
                child: SafeArea(
                  top: false,
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 400),
                    child: _currentPage == _pages.length - 1
                        ? Shimmer.fromColors(
                            baseColor: Colors.white,
                            highlightColor: Colors.yellow[200]!,
                            child: ElevatedButton(
                              key: const ValueKey('get-started'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.white,
                                foregroundColor: AppTheme.primaryColor,
                                minimumSize: const Size.fromHeight(56),
                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                                elevation: 8,
                              ),
                              onPressed: () async {
                                await languageService.completeOnboarding();
                                if (mounted) {
                                  context.go('/home');
                                }
                              },
                              child: Text(
                                LocalizationService.t('done', _selectedLanguage),
                                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18)
                              ),
                            ),
                          )
                        : Row(
                            children: [
                              if (_currentPage == 0) ...[
                                Expanded(
                                  child: OutlinedButton(
                                    key: const ValueKey('skip'),
                                    style: OutlinedButton.styleFrom(
                                      foregroundColor: Colors.white70,
                                      side: const BorderSide(color: Colors.white70, width: 1),
                                      minimumSize: const Size.fromHeight(56),
                                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                                    ),
                                    onPressed: () => _controller.nextPage(duration: const Duration(milliseconds: 500), curve: Curves.ease),
                                    child: Text(
                                      LocalizationService.t('skip', _selectedLanguage),
                                      style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16)
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                              ],
                              Expanded(
                                flex: _currentPage == 0 ? 2 : 1,
                                child: OutlinedButton(
                                  key: const ValueKey('next'),
                                  style: OutlinedButton.styleFrom(
                                    foregroundColor: Colors.white,
                                    backgroundColor: Colors.white.withValues(alpha: 0.1),
                                    side: const BorderSide(color: Colors.white, width: 2),
                                    minimumSize: const Size.fromHeight(56),
                                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                                  ),
                                  onPressed: () => _controller.nextPage(duration: const Duration(milliseconds: 500), curve: Curves.ease),
                                  child: Text(
                                    LocalizationService.t('next', _selectedLanguage),
                                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18)
                                  ),
                                ),
                              ),
                            ],
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _OnboardPage {
  final String title;
  final String description;
  final String image;
  final Gradient bgGradient;
  final bool isLanguagePage;

  const _OnboardPage({
    required this.title,
    required this.description,
    required this.image,
    required this.bgGradient,
    this.isLanguagePage = false,
  });
}
