import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../constants/theme.dart';
import 'package:lottie/lottie.dart';
import 'package:shimmer/shimmer.dart';
import 'package:godavari_pushkaralu_app/services/language_service.dart';
import 'package:godavari_pushkaralu_app/services/localization_service.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _controller = PageController();
  int _currentPage = 0;
  String _selectedLanguage = 'en';

  @override
  void initState() {
    super.initState();
    _selectedLanguage = languageService.currentLanguageCode;
    _checkOnboardingStatus();
  }

  void _checkOnboardingStatus() async {
    final isCompleted = await languageService.isOnboardingCompleted();
    if (isCompleted && mounted) {
      context.go('/home');
    }
  }

  List<_OnboardPage> get _pages => [
    // Language Selection Page
    _OnboardPage(
      title: LocalizationService.t('choose_language', _selectedLanguage),
      description: LocalizationService.t('language_subtitle', _selectedLanguage),
      image: 'assets/icons/A spiritual Indian style.png',
      bgGradient: const LinearGradient(
        colors: [Color(0xFF0D47A1), Color(0xFF42A5F5), Color(0xFFFFD700)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      isLanguagePage: true,
    ),
    _OnboardPage(
      title: LocalizationService.t('godavari_pushkaralu', _selectedLanguage),
      description: LocalizationService.t('sacred_river_festival', _selectedLanguage),
      image: 'assets/icons/A spiritual Indian style.png',
      bgGradient: const LinearGradient(
        colors: [Color(0xFF0D47A1), Color(0xFF42A5F5), Color(0xFFFFD700)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    _OnboardPage(
      title: 'Spiritual & Premium Experience',
      description: 'Daily puja, mantras, and premium services for a blessed festival.',
      image: 'assets/icons/placeholder_gold_badge.png',
      bgGradient: LinearGradient(
        colors: [Color(0xFFFFD700), Color(0xFFFFA726), Color(0xFF42A5F5)],
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
      ),
    ),
    _OnboardPage(
      title: 'Convenience at Every Step',
      description: 'Live queue, facilities, seva tracker, and more for your comfort.',
      image: 'assets/icons/placeholder_restroom.png',
      bgGradient: LinearGradient(
        colors: [Color(0xFFFFA726), Color(0xFF0D47A1), Color(0xFFFFD700)],
        begin: Alignment.bottomLeft,
        end: Alignment.topRight,
      ),
    ),
    _OnboardPage(
      title: 'Safety & Support',
      description: '24x7 emergency, medical, and help at your fingertips.',
      image: 'assets/icons/placeholder_medical.png',
      bgGradient: LinearGradient(
        colors: [Color(0xFF42A5F5), Color(0xFFFFA726), Color(0xFF0D47A1)],
        begin: Alignment.bottomRight,
        end: Alignment.topLeft,
      ),
    ),
  ];

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onPageChanged(int idx) {
    setState(() => _currentPage = idx);
  }

  Widget _buildLanguageSelection() {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            shape: BoxShape.circle,
          ),
          child: const Center(
            child: Text(
              'ॐ',
              style: TextStyle(
                color: Colors.white,
                fontSize: 40,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(height: 40),
        Text(
          LocalizationService.t('choose_language', _selectedLanguage),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 28,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          LocalizationService.t('language_subtitle', _selectedLanguage),
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.9),
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 40),
        ...LanguageService.supportedLanguages.map((language) {
          final isSelected = _selectedLanguage == language['code'];
          return Container(
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: () async {
                  setState(() {
                    _selectedLanguage = language['code']!;
                  });
                  await languageService.changeLanguage(language['code']!);
                },
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isSelected
                      ? Colors.white.withValues(alpha: 0.3)
                      : Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isSelected ? Colors.white : Colors.white.withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        language['flag']!,
                        style: const TextStyle(fontSize: 24),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              language['name']!,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              language['nativeName']!,
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.8),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (isSelected)
                        const Icon(
                          Icons.check_circle,
                          color: Colors.white,
                          size: 24,
                        ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final page = _pages[_currentPage];
    return Scaffold(
      body: AnimatedContainer(
        duration: const Duration(milliseconds: 600),
        curve: Curves.easeInOut,
        decoration: BoxDecoration(
          gradient: page.bgGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: PageView.builder(
                  controller: _controller,
                  itemCount: _pages.length,
                  onPageChanged: _onPageChanged,
                  itemBuilder: (context, idx) {
                    final p = _pages[idx];
                    return AnimatedOpacity(
                      opacity: _currentPage == idx ? 1.0 : 0.0,
                      duration: const Duration(milliseconds: 500),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (p.isLanguagePage) ...[
                            _buildLanguageSelection(),
                          ] else ...[
                            Hero(
                              tag: 'onboard-img-$idx',
                              child: Column(
                                children: [
                                  Lottie.asset('assets/lottie/onboard_anim.json', height: 120, repeat: true),
                                  const SizedBox(height: 8),
                                Container(
                                  margin: const EdgeInsets.only(bottom: 32),
                                  decoration: BoxDecoration(
                                    boxShadow: [BoxShadow(color: Colors.black26, blurRadius: 24, offset: Offset(0, 8))],
                                    borderRadius: BorderRadius.circular(32),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(32),
                                    child: Image.asset(
                                      p.image,
                                      height: 100,
                                      width: 100,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Text(
                            p.title,
                            textAlign: TextAlign.center,
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  shadows: [Shadow(color: Colors.black26, blurRadius: 8)],
                                ),
                          ),
                          const SizedBox(height: 18),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 32.0),
                            child: Text(
                              p.description,
                              textAlign: TextAlign.center,
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Colors.white.withOpacity(0.95),
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ),
                        ],
                          ],
                      ),
                    );
                  },
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(_pages.length, (idx) => AnimatedContainer(
                  duration: const Duration(milliseconds: 400),
                  margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 24),
                  width: _currentPage == idx ? 24 : 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: _currentPage == idx ? Colors.white : Colors.white54,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [if (_currentPage == idx) BoxShadow(color: Colors.black26, blurRadius: 6)],
                  ),
                )),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 400),
                  child: _currentPage == _pages.length - 1
                      ? Shimmer.fromColors(
                          baseColor: Colors.white,
                          highlightColor: Colors.yellow[200]!,
                          child: ElevatedButton(
                            key: const ValueKey('get-started'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: AppTheme.primaryColor,
                              minimumSize: const Size.fromHeight(48),
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                              elevation: 6,
                            ),
                            onPressed: () async {
                              await languageService.completeOnboarding();
                              if (mounted) {
                                context.go('/home');
                              }
                            },
                            child: Text(
                              LocalizationService.t('done', _selectedLanguage),
                              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18)
                            ),
                          ),
                        )
                      : OutlinedButton(
                          key: const ValueKey('next'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.white,
                            side: const BorderSide(color: Colors.white, width: 2),
                            minimumSize: const Size.fromHeight(48),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                          ),
                          onPressed: () => _controller.nextPage(duration: const Duration(milliseconds: 500), curve: Curves.ease),
                          child: Text(
                            LocalizationService.t('next', _selectedLanguage),
                            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18)
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _OnboardPage {
  final String title;
  final String description;
  final String image;
  final Gradient bgGradient;
  final bool isLanguagePage;

  const _OnboardPage({
    required this.title,
    required this.description,
    required this.image,
    required this.bgGradient,
    this.isLanguagePage = false,
  });
}
