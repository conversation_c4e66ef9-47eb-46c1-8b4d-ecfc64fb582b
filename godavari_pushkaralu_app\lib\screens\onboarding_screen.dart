import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../constants/theme.dart';
import 'package:lottie/lottie.dart';
import 'package:shimmer/shimmer.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _controller = PageController();
  int _currentPage = 0;

  final List<_OnboardPage> _pages = [
    _OnboardPage(
      title: 'Welcome to Godavari Pushkaralu 2027',
      description: 'A spiritual journey made easy, safe, and memorable for every devotee.',
      image: 'assets/icons/A spiritual Indian style.png',
      bgGradient: LinearGradient(
        colors: [Color(0xFF0D47A1), Color(0xFF42A5F5), Color(0xFFFFD700)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    _OnboardPage(
      title: 'Spiritual & Premium Experience',
      description: 'Daily puja, mantras, and premium services for a blessed festival.',
      image: 'assets/icons/placeholder_gold_badge.png',
      bgGradient: LinearGradient(
        colors: [Color(0xFFFFD700), Color(0xFFFFA726), Color(0xFF42A5F5)],
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
      ),
    ),
    _OnboardPage(
      title: 'Convenience at Every Step',
      description: 'Live queue, facilities, seva tracker, and more for your comfort.',
      image: 'assets/icons/placeholder_restroom.png',
      bgGradient: LinearGradient(
        colors: [Color(0xFFFFA726), Color(0xFF0D47A1), Color(0xFFFFD700)],
        begin: Alignment.bottomLeft,
        end: Alignment.topRight,
      ),
    ),
    _OnboardPage(
      title: 'Safety & Support',
      description: '24x7 emergency, medical, and help at your fingertips.',
      image: 'assets/icons/placeholder_medical.png',
      bgGradient: LinearGradient(
        colors: [Color(0xFF42A5F5), Color(0xFFFFA726), Color(0xFF0D47A1)],
        begin: Alignment.bottomRight,
        end: Alignment.topLeft,
      ),
    ),
  ];

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onPageChanged(int idx) {
    setState(() => _currentPage = idx);
  }

  @override
  Widget build(BuildContext context) {
    final page = _pages[_currentPage];
    return Scaffold(
      body: AnimatedContainer(
        duration: const Duration(milliseconds: 600),
        curve: Curves.easeInOut,
        decoration: BoxDecoration(
          gradient: page.bgGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: PageView.builder(
                  controller: _controller,
                  itemCount: _pages.length,
                  onPageChanged: _onPageChanged,
                  itemBuilder: (context, idx) {
                    final p = _pages[idx];
                    return AnimatedOpacity(
                      opacity: _currentPage == idx ? 1.0 : 0.0,
                      duration: const Duration(milliseconds: 500),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Hero(
                            tag: 'onboard-img-$idx',
                            child: Column(
                              children: [
                                Lottie.asset('assets/lottie/onboard_anim.json', height: 120, repeat: true),
                                const SizedBox(height: 8),
                                Container(
                                  margin: const EdgeInsets.only(bottom: 32),
                                  decoration: BoxDecoration(
                                    boxShadow: [BoxShadow(color: Colors.black26, blurRadius: 24, offset: Offset(0, 8))],
                                    borderRadius: BorderRadius.circular(32),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(32),
                                    child: Image.asset(
                                      p.image,
                                      height: 100,
                                      width: 100,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Text(
                            p.title,
                            textAlign: TextAlign.center,
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  shadows: [Shadow(color: Colors.black26, blurRadius: 8)],
                                ),
                          ),
                          const SizedBox(height: 18),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 32.0),
                            child: Text(
                              p.description,
                              textAlign: TextAlign.center,
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Colors.white.withOpacity(0.95),
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(_pages.length, (idx) => AnimatedContainer(
                  duration: const Duration(milliseconds: 400),
                  margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 24),
                  width: _currentPage == idx ? 24 : 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: _currentPage == idx ? Colors.white : Colors.white54,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [if (_currentPage == idx) BoxShadow(color: Colors.black26, blurRadius: 6)],
                  ),
                )),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 400),
                  child: _currentPage == _pages.length - 1
                      ? Shimmer.fromColors(
                          baseColor: Colors.white,
                          highlightColor: Colors.yellow[200]!,
                          child: ElevatedButton(
                            key: const ValueKey('get-started'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: AppTheme.primaryColor,
                              minimumSize: const Size.fromHeight(48),
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                              elevation: 6,
                            ),
                            onPressed: () => context.go('/home'),
                            child: const Text('Get Started', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                          ),
                        )
                      : OutlinedButton(
                          key: const ValueKey('next'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.white,
                            side: const BorderSide(color: Colors.white, width: 2),
                            minimumSize: const Size.fromHeight(48),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                          ),
                          onPressed: () => _controller.nextPage(duration: const Duration(milliseconds: 500), curve: Curves.ease),
                          child: const Text('Next', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _OnboardPage {
  final String title;
  final String description;
  final String image;
  final Gradient bgGradient;
  const _OnboardPage({
    required this.title,
    required this.description,
    required this.image,
    required this.bgGradient,
  });
}
