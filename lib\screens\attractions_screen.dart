import 'package:flutter/material.dart';

class AttractionsScreen extends StatelessWidget {
  const AttractionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nearby Attractions'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: <PERSON><PERSON><PERSON>(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Popular Attractions',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildAttractionCard(
              'Kaleshwaram Temple',
              'Sacred temple complex at the confluence of rivers',
              '0.5 km',
              Icons.temple_hindu,
              Colors.orange,
            ),
            
            _buildAttractionCard(
              'Medaram Jatara',
              'Famous tribal festival location',
              '45 km',
              Icons.festival,
              Colors.purple,
            ),
            
            _buildAttractionCard(
              'Warangal Fort',
              'Historic fort with ancient architecture',
              '120 km',
              Icons.castle,
              Colors.brown,
            ),
            
            _buildAttractionCard(
              'Thousand Pillar Temple',
              'Architectural marvel with intricate carvings',
              '125 km',
              Icons.architecture,
              Colors.indigo,
            ),
            
            _buildAttractionCard(
              'Ramappa Temple',
              'UNESCO World Heritage Site',
              '140 km',
              Icons.account_balance,
              Colors.teal,
            ),
            
            _buildAttractionCard(
              'Laknavaram Lake',
              'Scenic lake with suspension bridge',
              '80 km',
              Icons.water,
              Colors.blue,
            ),
            
            _buildAttractionCard(
              'Bhadrakali Temple',
              'Ancient temple dedicated to Goddess Bhadrakali',
              '110 km',
              Icons.temple_buddhist,
              Colors.red,
            ),
            
            _buildAttractionCard(
              'Pakhal Lake',
              'Beautiful lake surrounded by wildlife sanctuary',
              '160 km',
              Icons.nature,
              Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttractionCard(String name, String description, String distance, IconData icon, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 32,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        distance,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: () {
                          // Handle directions
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Opening directions to $name')),
                          );
                        },
                        child: const Text('Directions'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
