import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';

enum WeatherCondition { sunny, partlyCloudy, cloudy, rainy, thunderstorm, foggy }

class WeatherData {
  final String location;
  final double latitude;
  final double longitude;
  final double temperature;
  final double feelsLike;
  final int humidity;
  final double windSpeed;
  final String windDirection;
  final double pressure;
  final int visibility;
  final int uvIndex;
  final WeatherCondition condition;
  final String description;
  final DateTime sunrise;
  final DateTime sunset;
  final DateTime lastUpdated;

  WeatherData({
    required this.location,
    required this.latitude,
    required this.longitude,
    required this.temperature,
    required this.feelsLike,
    required this.humidity,
    required this.windSpeed,
    required this.windDirection,
    required this.pressure,
    required this.visibility,
    required this.uvIndex,
    required this.condition,
    required this.description,
    required this.sunrise,
    required this.sunset,
    required this.lastUpdated,
  });
}

class HourlyForecast {
  final DateTime time;
  final double temperature;
  final WeatherCondition condition;
  final int humidity;
  final double windSpeed;
  final int chanceOfRain;

  HourlyForecast({
    required this.time,
    required this.temperature,
    required this.condition,
    required this.humidity,
    required this.windSpeed,
    required this.chanceOfRain,
  });
}

class DailyForecast {
  final DateTime date;
  final double maxTemp;
  final double minTemp;
  final WeatherCondition condition;
  final String description;
  final int chanceOfRain;
  final double windSpeed;
  final int humidity;

  DailyForecast({
    required this.date,
    required this.maxTemp,
    required this.minTemp,
    required this.condition,
    required this.description,
    required this.chanceOfRain,
    required this.windSpeed,
    required this.humidity,
  });
}

class WeatherAlert {
  final String title;
  final String description;
  final String severity; // Low, Medium, High, Extreme
  final DateTime startTime;
  final DateTime endTime;
  final String type; // Rain, Heat, Wind, etc.

  WeatherAlert({
    required this.title,
    required this.description,
    required this.severity,
    required this.startTime,
    required this.endTime,
    required this.type,
  });
}

class WeatherService {
  static final WeatherService _instance = WeatherService._internal();
  factory WeatherService() => _instance;
  WeatherService._internal();

  final StreamController<WeatherData> _currentWeatherController = 
      StreamController<WeatherData>.broadcast();
  final StreamController<List<HourlyForecast>> _hourlyForecastController = 
      StreamController<List<HourlyForecast>>.broadcast();
  final StreamController<List<DailyForecast>> _dailyForecastController = 
      StreamController<List<DailyForecast>>.broadcast();
  final StreamController<List<WeatherAlert>> _alertsController = 
      StreamController<List<WeatherAlert>>.broadcast();

  Stream<WeatherData> get currentWeatherStream => _currentWeatherController.stream;
  Stream<List<HourlyForecast>> get hourlyForecastStream => _hourlyForecastController.stream;
  Stream<List<DailyForecast>> get dailyForecastStream => _dailyForecastController.stream;
  Stream<List<WeatherAlert>> get alertsStream => _alertsController.stream;

  Timer? _updateTimer;
  Position? _currentPosition;
  WeatherData? _currentWeather;
  List<HourlyForecast> _hourlyForecast = [];
  List<DailyForecast> _dailyForecast = [];
  List<WeatherAlert> _alerts = [];

  // Known locations for Pushkaralu
  final Map<String, Map<String, double>> _pushkaraluLocations = {
    'Rajahmundry': {'lat': 17.0081, 'lon': 81.7734},
    'Bhadrachalam': {'lat': 17.6688, 'lon': 80.8936},
    'Nashik': {'lat': 19.9975, 'lon': 73.7898},
    'Nanded': {'lat': 19.1383, 'lon': 77.2975},
    'Basara': {'lat': 18.1200, 'lon': 77.1400},
  };

  void initialize() {
    _getCurrentLocation();
    _startRealTimeUpdates();
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        // Use default location (Rajahmundry)
        _currentPosition = Position(
          latitude: 17.0081,
          longitude: 81.7734,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          altitudeAccuracy: 0,
          heading: 0,
          headingAccuracy: 0,
          speed: 0,
          speedAccuracy: 0,
        );
        await _fetchWeatherData();
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          // Use default location
          _currentPosition = Position(
            latitude: 17.0081,
            longitude: 81.7734,
            timestamp: DateTime.now(),
            accuracy: 0,
            altitude: 0,
            altitudeAccuracy: 0,
            heading: 0,
            headingAccuracy: 0,
            speed: 0,
            speedAccuracy: 0,
          );
          await _fetchWeatherData();
          return;
        }
      }

      _currentPosition = await Geolocator.getCurrentPosition();
      await _fetchWeatherData();
    } catch (e) {
      // Fallback to Rajahmundry
      _currentPosition = Position(
        latitude: 17.0081,
        longitude: 81.7734,
        timestamp: DateTime.now(),
        accuracy: 0,
        altitude: 0,
        altitudeAccuracy: 0,
        heading: 0,
        headingAccuracy: 0,
        speed: 0,
        speedAccuracy: 0,
      );
      await _fetchWeatherData();
    }
  }

  void _startRealTimeUpdates() {
    _updateTimer = Timer.periodic(const Duration(minutes: 10), (timer) {
      _fetchWeatherData();
    });
  }

  Future<void> _fetchWeatherData() async {
    if (_currentPosition == null) return;

    try {
      // For demo purposes, we'll use realistic simulated data based on location
      // In production, you would use a real weather API like OpenWeatherMap
      await _generateRealisticWeatherData();
    } catch (e) {
      // Generate fallback data
      await _generateRealisticWeatherData();
    }
  }

  Future<void> _generateRealisticWeatherData() async {
    final random = Random();
    final now = DateTime.now();
    
    // Determine location name
    String locationName = _getLocationName(_currentPosition!.latitude, _currentPosition!.longitude);
    
    // Generate realistic weather based on location and season
    final weatherData = _generateLocationBasedWeather(locationName, now, random);
    
    _currentWeather = weatherData;
    _currentWeatherController.add(weatherData);

    // Generate hourly forecast
    _hourlyForecast = _generateHourlyForecast(weatherData, random);
    _hourlyForecastController.add(_hourlyForecast);

    // Generate daily forecast
    _dailyForecast = _generateDailyForecast(weatherData, random);
    _dailyForecastController.add(_dailyForecast);

    // Generate weather alerts
    _alerts = _generateWeatherAlerts(weatherData, random);
    _alertsController.add(_alerts);
  }

  String _getLocationName(double lat, double lon) {
    double minDistance = double.infinity;
    String closestLocation = 'Rajahmundry';

    for (final entry in _pushkaraluLocations.entries) {
      final distance = Geolocator.distanceBetween(
        lat, lon,
        entry.value['lat']!, entry.value['lon']!,
      );
      if (distance < minDistance) {
        minDistance = distance;
        closestLocation = entry.key;
      }
    }

    return closestLocation;
  }

  WeatherData _generateLocationBasedWeather(String location, DateTime now, Random random) {
    // Base weather patterns for different locations
    Map<String, Map<String, dynamic>> locationWeather = {
      'Rajahmundry': {
        'baseTemp': 28.0,
        'humidity': 75,
        'windSpeed': 12.0,
        'commonCondition': WeatherCondition.partlyCloudy,
      },
      'Bhadrachalam': {
        'baseTemp': 26.0,
        'humidity': 80,
        'windSpeed': 8.0,
        'commonCondition': WeatherCondition.cloudy,
      },
      'Nashik': {
        'baseTemp': 24.0,
        'humidity': 65,
        'windSpeed': 15.0,
        'commonCondition': WeatherCondition.sunny,
      },
      'Nanded': {
        'baseTemp': 27.0,
        'humidity': 70,
        'windSpeed': 10.0,
        'commonCondition': WeatherCondition.partlyCloudy,
      },
      'Basara': {
        'baseTemp': 25.0,
        'humidity': 78,
        'windSpeed': 9.0,
        'commonCondition': WeatherCondition.cloudy,
      },
    };

    final locationData = locationWeather[location] ?? locationWeather['Rajahmundry']!;
    
    // Add realistic variations
    final temperature = locationData['baseTemp'] + random.nextDouble() * 8 - 4; // ±4°C variation
    final humidity = (locationData['humidity'] + random.nextInt(20) - 10).clamp(40, 95);
    final windSpeed = locationData['windSpeed'] + random.nextDouble() * 10 - 5;
    
    // Determine condition based on time and randomness
    WeatherCondition condition = locationData['commonCondition'];
    if (random.nextDouble() < 0.3) {
      // 30% chance of different weather
      final conditions = WeatherCondition.values;
      condition = conditions[random.nextInt(conditions.length)];
    }

    return WeatherData(
      location: location,
      latitude: _currentPosition!.latitude,
      longitude: _currentPosition!.longitude,
      temperature: temperature,
      feelsLike: temperature + random.nextDouble() * 4 - 2,
      humidity: humidity,
      windSpeed: windSpeed.clamp(0, 30),
      windDirection: ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'][random.nextInt(8)],
      pressure: 1013 + random.nextInt(40) - 20,
      visibility: 8 + random.nextInt(7),
      uvIndex: _calculateUVIndex(now),
      condition: condition,
      description: _getWeatherDescription(condition),
      sunrise: DateTime(now.year, now.month, now.day, 6, 15 + random.nextInt(30)),
      sunset: DateTime(now.year, now.month, now.day, 18, 30 + random.nextInt(30)),
      lastUpdated: now,
    );
  }

  int _calculateUVIndex(DateTime now) {
    final hour = now.hour;
    if (hour < 6 || hour > 18) return 0;
    if (hour < 8 || hour > 16) return 2;
    if (hour < 10 || hour > 14) return 5;
    return 8; // Peak UV hours
  }

  String _getWeatherDescription(WeatherCondition condition) {
    switch (condition) {
      case WeatherCondition.sunny:
        return 'Clear and sunny';
      case WeatherCondition.partlyCloudy:
        return 'Partly cloudy';
      case WeatherCondition.cloudy:
        return 'Overcast';
      case WeatherCondition.rainy:
        return 'Light rain';
      case WeatherCondition.thunderstorm:
        return 'Thunderstorms';
      case WeatherCondition.foggy:
        return 'Foggy conditions';
    }
  }

  List<HourlyForecast> _generateHourlyForecast(WeatherData current, Random random) {
    final forecasts = <HourlyForecast>[];
    final baseTime = DateTime.now();

    for (int i = 1; i <= 24; i++) {
      final time = baseTime.add(Duration(hours: i));
      final tempVariation = random.nextDouble() * 6 - 3; // ±3°C variation
      final temperature = (current.temperature + tempVariation).clamp(15, 45);

      // Weather tends to be more stable in short term
      WeatherCondition condition = current.condition;
      if (random.nextDouble() < 0.2) {
        final conditions = WeatherCondition.values;
        condition = conditions[random.nextInt(conditions.length)];
      }

      forecasts.add(HourlyForecast(
        time: time,
        temperature: temperature.toDouble(),
        condition: condition,
        humidity: (current.humidity + random.nextInt(20) - 10).clamp(30, 95),
        windSpeed: (current.windSpeed + random.nextDouble() * 10 - 5).clamp(0.0, 30.0),
        chanceOfRain: _calculateRainChance(condition, random),
      ));
    }

    return forecasts;
  }

  List<DailyForecast> _generateDailyForecast(WeatherData current, Random random) {
    final forecasts = <DailyForecast>[];
    final baseDate = DateTime.now();

    for (int i = 1; i <= 7; i++) {
      final date = baseDate.add(Duration(days: i));
      final tempVariation = random.nextDouble() * 8 - 4; // ±4°C variation
      final maxTemp = (current.temperature + tempVariation + 3).clamp(20, 45);
      final minTemp = (maxTemp - 8 - random.nextDouble() * 4).clamp(15, 40);

      // Weather can change more over days
      WeatherCondition condition = current.condition;
      if (random.nextDouble() < 0.4) {
        final conditions = WeatherCondition.values;
        condition = conditions[random.nextInt(conditions.length)];
      }

      forecasts.add(DailyForecast(
        date: date,
        maxTemp: maxTemp.toDouble(),
        minTemp: minTemp.toDouble(),
        condition: condition,
        description: _getWeatherDescription(condition),
        chanceOfRain: _calculateRainChance(condition, random),
        windSpeed: (current.windSpeed + random.nextDouble() * 10 - 5).clamp(0.0, 30.0),
        humidity: (current.humidity + random.nextInt(20) - 10).clamp(40, 90),
      ));
    }

    return forecasts;
  }

  List<WeatherAlert> _generateWeatherAlerts(WeatherData current, Random random) {
    final alerts = <WeatherAlert>[];
    final now = DateTime.now();

    // Generate realistic alerts based on weather conditions
    if (current.temperature > 35) {
      alerts.add(WeatherAlert(
        title: 'Heat Wave Warning',
        description: 'High temperatures expected. Stay hydrated and avoid prolonged sun exposure during Pushkaralu activities.',
        severity: 'High',
        startTime: now,
        endTime: now.add(const Duration(hours: 8)),
        type: 'Heat',
      ));
    }

    if (current.condition == WeatherCondition.rainy || current.condition == WeatherCondition.thunderstorm) {
      alerts.add(WeatherAlert(
        title: 'Rain Alert',
        description: 'Rain expected during Pushkaralu. Carry umbrellas and be cautious near ghats.',
        severity: 'Medium',
        startTime: now,
        endTime: now.add(const Duration(hours: 4)),
        type: 'Rain',
      ));
    }

    if (current.windSpeed > 20) {
      alerts.add(WeatherAlert(
        title: 'Strong Wind Advisory',
        description: 'Strong winds may affect outdoor activities and boat services.',
        severity: 'Medium',
        startTime: now,
        endTime: now.add(const Duration(hours: 6)),
        type: 'Wind',
      ));
    }

    if (current.visibility < 5) {
      alerts.add(WeatherAlert(
        title: 'Low Visibility Warning',
        description: 'Foggy conditions may affect transportation and visibility near water bodies.',
        severity: 'Medium',
        startTime: now,
        endTime: now.add(const Duration(hours: 3)),
        type: 'Fog',
      ));
    }

    return alerts;
  }

  int _calculateRainChance(WeatherCondition condition, Random random) {
    switch (condition) {
      case WeatherCondition.sunny:
        return random.nextInt(10);
      case WeatherCondition.partlyCloudy:
        return 10 + random.nextInt(20);
      case WeatherCondition.cloudy:
        return 20 + random.nextInt(30);
      case WeatherCondition.rainy:
        return 60 + random.nextInt(30);
      case WeatherCondition.thunderstorm:
        return 80 + random.nextInt(20);
      case WeatherCondition.foggy:
        return 30 + random.nextInt(20);
    }
  }

  // Public methods for getting weather data
  WeatherData? get currentWeather => _currentWeather;
  List<HourlyForecast> get hourlyForecast => List.from(_hourlyForecast);
  List<DailyForecast> get dailyForecast => List.from(_dailyForecast);
  List<WeatherAlert> get alerts => List.from(_alerts);

  Future<void> refreshWeather() async {
    await _getCurrentLocation();
    await _fetchWeatherData();
  }

  Future<WeatherData?> getWeatherForLocation(String locationName) async {
    final locationCoords = _pushkaraluLocations[locationName];
    if (locationCoords == null) return null;

    final random = Random();
    return _generateLocationBasedWeather(locationName, DateTime.now(), random);
  }

  bool isGoodForHolyDip() {
    if (_currentWeather == null) return true;

    // Good conditions for holy dip
    return _currentWeather!.condition != WeatherCondition.thunderstorm &&
           _currentWeather!.condition != WeatherCondition.rainy &&
           _currentWeather!.windSpeed < 25 &&
           _currentWeather!.temperature > 18 &&
           _currentWeather!.temperature < 40;
  }

  String getHolyDipRecommendation() {
    if (_currentWeather == null) return 'Weather data loading...';

    if (isGoodForHolyDip()) {
      if (_currentWeather!.condition == WeatherCondition.sunny) {
        return 'Excellent conditions for holy dip! Clear skies and pleasant weather.';
      } else {
        return 'Good conditions for holy dip. Weather is favorable.';
      }
    } else {
      if (_currentWeather!.condition == WeatherCondition.thunderstorm) {
        return 'Not recommended due to thunderstorms. Please wait for better conditions.';
      } else if (_currentWeather!.condition == WeatherCondition.rainy) {
        return 'Light rain present. Consider waiting or use covered areas.';
      } else if (_currentWeather!.temperature > 40) {
        return 'Very hot conditions. Early morning or evening dip recommended.';
      } else if (_currentWeather!.windSpeed > 25) {
        return 'Strong winds present. Exercise caution near water.';
      } else {
        return 'Weather conditions require caution. Check latest updates.';
      }
    }
  }

  void dispose() {
    _updateTimer?.cancel();
    _currentWeatherController.close();
    _hourlyForecastController.close();
    _dailyForecastController.close();
    _alertsController.close();
  }
}
