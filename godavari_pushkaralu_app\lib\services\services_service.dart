import 'dart:async';
import 'dart:math';

enum ServiceCategory { puja, darshan, accommodation, transportation, special }

enum ServiceStatus { available, limited, unavailable, waitlist }

class ServiceItem {
  final String id;
  final String name;
  final String description;
  final ServiceCategory category;
  final double basePrice;
  final double currentPrice;
  final ServiceStatus status;
  final int availableSlots;
  final int totalSlots;
  final DateTime? nextAvailable;
  final List<String> features;
  final String duration;
  final bool isPopular;
  final double rating;
  final int bookingsToday;

  ServiceItem({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.basePrice,
    required this.currentPrice,
    required this.status,
    required this.availableSlots,
    required this.totalSlots,
    this.nextAvailable,
    required this.features,
    required this.duration,
    this.isPopular = false,
    required this.rating,
    this.bookingsToday = 0,
  });

  ServiceItem copyWith({
    ServiceStatus? status,
    int? availableSlots,
    double? currentPrice,
    DateTime? nextAvailable,
    int? bookingsToday,
  }) {
    return ServiceItem(
      id: id,
      name: name,
      description: description,
      category: category,
      basePrice: basePrice,
      currentPrice: currentPrice ?? this.currentPrice,
      status: status ?? this.status,
      availableSlots: availableSlots ?? this.availableSlots,
      totalSlots: totalSlots,
      nextAvailable: nextAvailable ?? this.nextAvailable,
      features: features,
      duration: duration,
      isPopular: isPopular,
      rating: rating,
      bookingsToday: bookingsToday ?? this.bookingsToday,
    );
  }
}

class ServicesService {
  static final ServicesService _instance = ServicesService._internal();
  factory ServicesService() => _instance;
  ServicesService._internal();

  final StreamController<Map<String, ServiceItem>> _servicesController = 
      StreamController<Map<String, ServiceItem>>.broadcast();
  
  Stream<Map<String, ServiceItem>> get servicesStream => _servicesController.stream;
  
  Timer? _updateTimer;
  Map<String, ServiceItem> _services = {};

  void initialize() {
    _initializeServices();
    _startRealTimeUpdates();
  }

  void _initializeServices() {
    _services = {
      // Puja Services
      'daily_puja': ServiceItem(
        id: 'daily_puja',
        name: 'Daily Puja',
        description: 'Morning and evening prayers with aarti',
        category: ServiceCategory.puja,
        basePrice: 501.0,
        currentPrice: 501.0,
        status: ServiceStatus.available,
        availableSlots: 25,
        totalSlots: 50,
        features: ['Aarti participation', 'Prasadam included', 'Photo allowed'],
        duration: '45 minutes',
        rating: 4.8,
        bookingsToday: 25,
      ),
      'special_puja': ServiceItem(
        id: 'special_puja',
        name: 'Special Puja',
        description: 'Customized puja for specific wishes',
        category: ServiceCategory.puja,
        basePrice: 1501.0,
        currentPrice: 1501.0,
        status: ServiceStatus.available,
        availableSlots: 8,
        totalSlots: 15,
        features: ['Personal priest', 'Custom mantras', 'Sacred items included'],
        duration: '90 minutes',
        isPopular: true,
        rating: 4.9,
        bookingsToday: 7,
      ),
      'homam_service': ServiceItem(
        id: 'homam_service',
        name: 'Homam Service',
        description: 'Sacred fire ceremony for prosperity',
        category: ServiceCategory.puja,
        basePrice: 2501.0,
        currentPrice: 2501.0,
        status: ServiceStatus.limited,
        availableSlots: 2,
        totalSlots: 5,
        features: ['Sacred fire ritual', 'Vedic chanting', 'Blessed offerings'],
        duration: '2 hours',
        rating: 4.7,
        bookingsToday: 3,
      ),

      // Darshan Services
      'vip_darshan': ServiceItem(
        id: 'vip_darshan',
        name: 'VIP Darshan',
        description: 'Priority darshan with minimal waiting',
        category: ServiceCategory.darshan,
        basePrice: 201.0,
        currentPrice: 251.0, // Dynamic pricing
        status: ServiceStatus.available,
        availableSlots: 15,
        totalSlots: 30,
        features: ['Skip queue', 'Dedicated entrance', 'Photo opportunity'],
        duration: '30 minutes',
        isPopular: true,
        rating: 4.6,
        bookingsToday: 15,
      ),
      'special_darshan': ServiceItem(
        id: 'special_darshan',
        name: 'Special Darshan',
        description: 'Organized darshan at specific times',
        category: ServiceCategory.darshan,
        basePrice: 101.0,
        currentPrice: 101.0,
        status: ServiceStatus.available,
        availableSlots: 45,
        totalSlots: 100,
        features: ['Guided tour', 'Group darshan', 'Information booklet'],
        duration: '45 minutes',
        rating: 4.4,
        bookingsToday: 55,
      ),

      // Accommodation
      'dharamshala': ServiceItem(
        id: 'dharamshala',
        name: 'Dharamshala',
        description: 'Basic accommodation for pilgrims',
        category: ServiceCategory.accommodation,
        basePrice: 300.0,
        currentPrice: 350.0, // Peak season pricing
        status: ServiceStatus.limited,
        availableSlots: 5,
        totalSlots: 50,
        features: ['Shared rooms', 'Basic amenities', 'Meals available'],
        duration: 'Per night',
        rating: 4.2,
        bookingsToday: 45,
      ),
      'guest_house': ServiceItem(
        id: 'guest_house',
        name: 'Guest House',
        description: 'Comfortable private rooms with modern amenities',
        category: ServiceCategory.accommodation,
        basePrice: 800.0,
        currentPrice: 950.0, // Peak pricing
        status: ServiceStatus.available,
        availableSlots: 12,
        totalSlots: 25,
        features: ['Private rooms', 'AC/Heating', 'Attached bathroom', 'WiFi'],
        duration: 'Per night',
        isPopular: true,
        rating: 4.7,
        bookingsToday: 13,
      ),

      // Transportation
      'shuttle_service': ServiceItem(
        id: 'shuttle_service',
        name: 'Shuttle Service',
        description: 'Regular bus service to main ghats',
        category: ServiceCategory.transportation,
        basePrice: 50.0,
        currentPrice: 50.0,
        status: ServiceStatus.available,
        availableSlots: 120,
        totalSlots: 200,
        features: ['AC buses', 'Multiple stops', 'Every 30 minutes'],
        duration: '45 minutes',
        rating: 4.3,
        bookingsToday: 80,
      ),
      'private_vehicle': ServiceItem(
        id: 'private_vehicle',
        name: 'Private Vehicle',
        description: 'Dedicated car with driver for full day',
        category: ServiceCategory.transportation,
        basePrice: 1200.0,
        currentPrice: 1400.0, // Peak demand
        status: ServiceStatus.limited,
        availableSlots: 3,
        totalSlots: 15,
        features: ['AC car', 'Experienced driver', 'Flexible timing', 'Fuel included'],
        duration: 'Full day',
        rating: 4.8,
        bookingsToday: 12,
      ),
    };

    _servicesController.add(_services);
  }

  void _startRealTimeUpdates() {
    _updateTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _updateServiceAvailability();
    });
  }

  void _updateServiceAvailability() {
    final random = Random();
    final now = DateTime.now();
    
    _services = _services.map((key, service) {
      // Simulate real-time changes
      int newAvailableSlots = service.availableSlots;
      ServiceStatus newStatus = service.status;
      double newPrice = service.currentPrice;
      int newBookings = service.bookingsToday;

      // Random booking activity
      if (random.nextDouble() < 0.3) {
        if (service.availableSlots > 0) {
          newAvailableSlots = max(0, service.availableSlots - random.nextInt(3));
          newBookings = service.bookingsToday + random.nextInt(2);
        }
      }

      // Update status based on availability
      if (newAvailableSlots == 0) {
        newStatus = ServiceStatus.unavailable;
      } else if (newAvailableSlots <= service.totalSlots * 0.2) {
        newStatus = ServiceStatus.limited;
      } else {
        newStatus = ServiceStatus.available;
      }

      // Dynamic pricing based on demand
      double demandRatio = 1.0 - (newAvailableSlots / service.totalSlots);
      if (demandRatio > 0.8) {
        newPrice = service.basePrice * 1.25; // 25% increase for high demand
      } else if (demandRatio > 0.6) {
        newPrice = service.basePrice * 1.15; // 15% increase for medium demand
      } else {
        newPrice = service.basePrice;
      }

      return MapEntry(key, service.copyWith(
        availableSlots: newAvailableSlots,
        status: newStatus,
        currentPrice: newPrice,
        bookingsToday: newBookings,
      ));
    });

    _servicesController.add(_services);
  }

  Future<bool> bookService(String serviceId, int quantity) async {
    final service = _services[serviceId];
    if (service == null || service.availableSlots < quantity) {
      return false;
    }

    // Simulate booking process
    await Future.delayed(const Duration(seconds: 2));

    _services[serviceId] = service.copyWith(
      availableSlots: service.availableSlots - quantity,
      bookingsToday: service.bookingsToday + quantity,
    );

    _servicesController.add(_services);
    return true;
  }

  Map<String, ServiceItem> get currentServices => Map.from(_services);

  List<ServiceItem> getServicesByCategory(ServiceCategory category) {
    return _services.values.where((service) => service.category == category).toList();
  }

  void dispose() {
    _updateTimer?.cancel();
    _servicesController.close();
  }
}
