import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:godavari_pushkaralu_app/screens/medical_services_screen.dart';
import 'package:godavari_pushkaralu_app/screens/weather_forecast_screen.dart';

void main() {
  group('Medical Services Screen Tests', () {
    testWidgets('Medical Services Screen displays correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const MedicalServicesScreen(),
        ),
      );

      // Verify the header is displayed
      expect(find.text('Medical Services'), findsOneWidget);
      
      // Verify emergency section is displayed
      expect(find.text('Emergency'), findsOneWidget);
      expect(find.text('24/7 Medical Assistance'), findsOneWidget);
      expect(find.text('Call 108'), findsOneWidget);
      
      // Verify emergency medical grid is displayed
      expect(find.text('Emergency Medical'), findsOneWidget);
      expect(find.text('Hospitals'), findsOneWidget);
      expect(find.text('Ambulance'), findsOneWidget);
      expect(find.text('Contacts'), findsOneWidget);
      expect(find.text('Blood Bank'), findsOneWidget);
      
      // Verify health services section is displayed
      expect(find.text('Health Services'), findsOneWidget);
      expect(find.text('First Aid Centers'), findsOneWidget);
      expect(find.text('Mobile Medical Units'), findsOneWidget);
      expect(find.text('Vaccination Centers'), findsOneWidget);
      
      // Verify medical information section is displayed
      expect(find.text('Medical Information'), findsOneWidget);
      expect(find.text('Health Tips'), findsOneWidget);
      expect(find.text('Medications'), findsOneWidget);
      expect(find.text('Insurance'), findsOneWidget);
      
      // Verify telemedicine section is displayed
      expect(find.text('Telemedicine'), findsOneWidget);
      expect(find.text('Online Consultation'), findsOneWidget);
      expect(find.text('Start Consultation'), findsOneWidget);
      
      // Verify health alerts section is displayed
      expect(find.text('Health Alerts'), findsOneWidget);
      expect(find.text('Water Quality Alert'), findsOneWidget);
      expect(find.text('Air Quality Warning'), findsOneWidget);
    });
  });

  group('Weather Forecast Screen Tests', () {
    testWidgets('Weather Forecast Screen displays correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const WeatherForecastScreen(),
        ),
      );

      // Verify the header is displayed
      expect(find.text('Weather Forecast'), findsOneWidget);
      
      // Verify location is displayed
      expect(find.text('Godavari Ghats, Rajahmundry'), findsOneWidget);
      
      // Verify current weather is displayed
      expect(find.text('28°C'), findsOneWidget);
      expect(find.text('Partly Cloudy'), findsOneWidget);
      expect(find.text('Feels like 32°C'), findsOneWidget);
      
      // Verify weather stats are displayed
      expect(find.text('Humidity'), findsOneWidget);
      expect(find.text('68%'), findsOneWidget);
      expect(find.text('Wind'), findsOneWidget);
      expect(find.text('12 km/h'), findsOneWidget);
      expect(find.text('Visibility'), findsOneWidget);
      expect(find.text('10 km'), findsOneWidget);
      
      // Verify hourly forecast is displayed
      expect(find.text('Hourly Forecast'), findsOneWidget);
      expect(find.text('Now'), findsOneWidget);
      
      // Verify 7-day forecast is displayed
      expect(find.text('7-Day Forecast'), findsOneWidget);
      expect(find.text('Today'), findsOneWidget);
      expect(find.text('Mon'), findsOneWidget);
      expect(find.text('Tue'), findsOneWidget);
      expect(find.text('Wed'), findsOneWidget);
      
      // Verify holy dip conditions are displayed
      expect(find.text('Holy Dip Conditions'), findsOneWidget);
      expect(find.text('Best Dip Times'), findsOneWidget);
      expect(find.text('Comfort Index'), findsOneWidget);
      expect(find.text('Photography'), findsOneWidget);
      expect(find.text('Travel Status'), findsOneWidget);
      
      // Verify weather alerts are displayed
      expect(find.text('Weather Alerts'), findsOneWidget);
      expect(find.text('Heavy Rain Alert'), findsOneWidget);
      expect(find.text('Air Quality Index'), findsOneWidget);
      expect(find.text('UV Index High'), findsOneWidget);
    });
  });
}
