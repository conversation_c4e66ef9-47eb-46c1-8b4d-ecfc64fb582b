import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:godavari_pushkaralu_app/constants/theme.dart';
import 'package:godavari_pushkaralu_app/services/navigation_service.dart';
import 'package:godavari_pushkaralu_app/services/language_service.dart';
// import 'package:godavari_pushkaralu_app/services/auth_service.dart';
// import 'package:godavari_pushkaralu_app/services/firebase_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize language service
  await languageService.initializeLanguage();

  // TODO: Initialize Firebase when configuration is ready
  // await FirebaseService.initializeFirebase();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: languageService,
      builder: (context, child) {
        return MaterialApp.router(
          title: '<PERSON><PERSON><PERSON> P<PERSON>lu 2027',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          routerConfig: NavigationService.router,
          locale: languageService.currentLocale,
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en'), // English
            Locale('te'), // Telugu
            Locale('hi'), // Hindi
            Locale('ta'), // Tamil
          ],
        );
      },
    );
  }
}
