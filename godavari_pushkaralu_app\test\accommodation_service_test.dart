import 'package:flutter_test/flutter_test.dart';
import 'package:godava<PERSON>_pushkaralu_app/services/accommodation_service.dart';

void main() {
  group('AccommodationService Tests', () {
    AccommodationService createService() {
      final service = AccommodationService.newInstance();
      service.initialize();
      return service;
    }

    test('should initialize with accommodations', () {
      final service = createService();
      expect(service.accommodations.isNotEmpty, true);
      expect(service.foodServices.isNotEmpty, true);
      service.dispose();
    });

    test('should have different accommodation types', () {
      final service = createService();
      final accommodations = service.accommodations;

      final dharamshalas = accommodations.where((acc) => acc.type == AccommodationType.dharamshala).toList();
      final guestHouses = accommodations.where((acc) => acc.type == AccommodationType.guestHouse).toList();
      final hotels = accommodations.where((acc) => acc.type == AccommodationType.hotel).toList();
      final homestays = accommodations.where((acc) => acc.type == AccommodationType.homestay).toList();

      expect(dharamshalas.isNotEmpty, true);
      expect(guestHouses.isNotEmpty, true);
      expect(hotels.isNotEmpty, true);
      expect(homestays.isNotEmpty, true);
      service.dispose();
    });

    test('should filter accommodations by type', () {
      final service = createService();
      final dharamshalas = service.getAccommodationsByType(AccommodationType.dharamshala);
      expect(dharamshalas.every((acc) => acc.type == AccommodationType.dharamshala), true);
      service.dispose();
    });

    test('should get available accommodations', () {
      final service = createService();
      final available = service.getAvailableAccommodations();
      expect(available.every((acc) => acc.status != AvailabilityStatus.full), true);
      service.dispose();
    });

    test('should get nearby accommodations', () {
      final service = createService();
      final nearby = service.getNearbyAccommodations(1.0); // Within 1 km
      expect(nearby.every((acc) => acc.distanceFromGhat <= 1.0), true);
      service.dispose();
    });

    test('should get annadanam services', () {
      final service = createService();
      final annadanam = service.getAnnadanamServices();
      expect(annadanam.every((fs) => fs.isAnnadanam), true);
      service.dispose();
    });

    test('should simulate booking', () async {
      final service = createService();
      final accommodation = service.accommodations.first;
      final initialRooms = accommodation.availableRooms;

      if (initialRooms > 0) {
        final request = BookingRequest(
          accommodationId: accommodation.id,
          checkIn: DateTime.now().add(const Duration(days: 1)),
          checkOut: DateTime.now().add(const Duration(days: 2)),
          guests: 2,
          guestName: 'Test User',
          guestPhone: '+91-9876543210',
          guestEmail: '<EMAIL>',
        );

        final bookingId = await service.bookAccommodation(request);
        expect(bookingId.isNotEmpty, true);
        expect(bookingId.startsWith('BK'), true);

        // Check if rooms were decremented
        final updatedAccommodation = service.accommodations.firstWhere((acc) => acc.id == accommodation.id);
        expect(updatedAccommodation.availableRooms, initialRooms - 1);
      }
      service.dispose();
    });

    test('should handle booking when no rooms available', () async {
      final service = createService();
      // Find an accommodation with no available rooms or create a scenario
      final accommodation = service.accommodations.firstWhere(
        (acc) => acc.availableRooms == 0,
        orElse: () => service.accommodations.first.copyWith(availableRooms: 0),
      );

      final request = BookingRequest(
        accommodationId: accommodation.id,
        checkIn: DateTime.now().add(const Duration(days: 1)),
        checkOut: DateTime.now().add(const Duration(days: 2)),
        guests: 2,
        guestName: 'Test User',
        guestPhone: '+91-9876543210',
        guestEmail: '<EMAIL>',
      );

      expect(
        () async => await service.bookAccommodation(request),
        throwsException,
      );
      service.dispose();
    });

    test('should have real-time updates stream', () async {
      final service = createService();
      bool streamReceived = false;

      service.accommodationsStream.listen((accommodations) {
        streamReceived = true;
        expect(accommodations.isNotEmpty, true);
      });

      // Wait a bit for the stream to emit
      await Future.delayed(const Duration(milliseconds: 100));
      expect(streamReceived, true);
      service.dispose();
    });

    test('should have food services stream', () async {
      final service = createService();
      bool streamReceived = false;

      service.foodServicesStream.listen((foodServices) {
        streamReceived = true;
        expect(foodServices.isNotEmpty, true);
      });

      // Wait a bit for the stream to emit
      await Future.delayed(const Duration(milliseconds: 100));
      expect(streamReceived, true);
      service.dispose();
    });
  });
}
