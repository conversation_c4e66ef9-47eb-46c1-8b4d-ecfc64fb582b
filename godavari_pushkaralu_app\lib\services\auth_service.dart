// Temporarily commented out due to Firebase dependency issues
// import 'package:flutter/foundation.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:google_sign_in/google_sign_in.dart';

// class AuthService extends ChangeNotifier {
//   final FirebaseAuth _auth = FirebaseAuth.instance;
//   final GoogleSignIn _googleSignIn = GoogleSignIn();
  
//   User? get currentUser => _auth.currentUser;
//   bool get isAuthenticated => currentUser != null;
  
//   Future<UserCredential> signInWithEmailPassword(String email, String password) async {
//     try {
//       final credential = await _auth.signInWithEmailAndPassword(
//         email: email,
//         password: password,
//       );
//       notifyListeners();
//       return credential;
//     } catch (e) {
//       rethrow;
//     }
//   }

//   Future<UserCredential> signUpWithEmailPassword(String email, String password) async {
//     try {
//       final credential = await _auth.createUserWithEmailAndPassword(
//         email: email,
//         password: password,
//       );
//       notifyListeners();
//       return credential;
//     } catch (e) {
//       rethrow;
//     }
//   }

//   Future<UserCredential> signInWithGoogle() async {
//     try {
//       final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
//       if (googleUser == null) throw Exception('Google sign in aborted');

//       final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
//       final credential = GoogleAuthProvider.credential(
//         accessToken: googleAuth.accessToken,
//         idToken: googleAuth.idToken,
//       );

//       final userCredential = await _auth.signInWithCredential(credential);
//       notifyListeners();
//       return userCredential;
//     } catch (e) {
//       rethrow;
//     }
//   }

//   Future<void> signOut() async {
//     try {
//       await Future.wait([
//         _auth.signOut(),
//         _googleSignIn.signOut(),
//       ]);
//       notifyListeners();
//     } catch (e) {
//       rethrow;
//     }
//   }

//   Future<void> resetPassword(String email) async {
//     try {
//       await _auth.sendPasswordResetEmail(email: email);
//     } catch (e) {
//       rethrow;
//     }
//   }

//   Future<void> updateProfile({String? displayName, String? photoURL}) async {
//     try {
//       await currentUser?.updateDisplayName(displayName);
//       await currentUser?.updatePhotoURL(photoURL);
//       notifyListeners();
//     } catch (e) {
//       rethrow;
//     }
//   }
// } 