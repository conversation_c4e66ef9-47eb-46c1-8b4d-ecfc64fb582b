import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:godavari_pushkaralu_app/services/language_service.dart';
import 'package:godavari_pushkaralu_app/services/localization_service.dart';

class AnimatedSidebar extends StatefulWidget {
  final String currentRoute;
  final VoidCallback onClose;

  const AnimatedSidebar({
    super.key,
    required this.currentRoute,
    required this.onClose,
  });

  @override
  State<AnimatedSidebar> createState() => _AnimatedSidebarState();
}

class _AnimatedSidebarState extends State<AnimatedSidebar>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(-1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    _slideController.forward();
    _fadeController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _closeSidebar() async {
    await _slideController.reverse();
    await _fadeController.reverse();
    widget.onClose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Background overlay
        AnimatedBuilder(
          animation: _fadeAnimation,
          builder: (context, child) {
            return GestureDetector(
              onTap: _closeSidebar,
              child: Container(
                color: Colors.black.withValues(alpha: _fadeAnimation.value),
              ),
            );
          },
        ),
        // Sidebar
        SlideTransition(
          position: _slideAnimation,
          child: Container(
            width: 280,
            height: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF1976D2),
                  Color(0xFF42A5F5),
                  Color(0xFFFFB74D),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 10,
                  offset: Offset(2, 0),
                ),
              ],
            ),
            child: SafeArea(
              child: Column(
                children: [
                  _buildHeader(),
                  Expanded(
                    child: _buildMenuItems(),
                  ),
                  _buildFooter(),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: const Center(
                  child: Text(
                    'ॐ',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      LocalizationService.t('godavari_pushkaralu', languageService.currentLanguageCode),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      LocalizationService.t('sacred_river_festival', languageService.currentLanguageCode),
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: _closeSidebar,
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Container(
            height: 1,
            color: Colors.white.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItems() {
    final currentLanguage = languageService.currentLanguageCode;
    final menuItems = [
      {
        'icon': Icons.home,
        'title': LocalizationService.t('home', currentLanguage),
        'subtitle': LocalizationService.t('home', currentLanguage),
        'route': '/home',
      },
      {
        'icon': Icons.hotel,
        'title': LocalizationService.t('stay_food', currentLanguage),
        'subtitle': LocalizationService.t('stay_food', currentLanguage),
        'route': '/stay',
      },
      {
        'icon': Icons.directions_bus,
        'title': LocalizationService.t('transportation', currentLanguage),
        'subtitle': LocalizationService.t('transportation', currentLanguage),
        'route': '/transportation',
      },
      {
        'icon': Icons.local_hospital,
        'title': LocalizationService.t('medical_services', currentLanguage),
        'subtitle': LocalizationService.t('medical_services', currentLanguage),
        'route': '/medical',
      },
      {
        'icon': Icons.cloud,
        'title': LocalizationService.t('weather_forecast', currentLanguage),
        'subtitle': LocalizationService.t('weather_forecast', currentLanguage),
        'route': '/weather',
      },
      {
        'icon': Icons.calendar_today,
        'title': LocalizationService.t('schedule', currentLanguage),
        'subtitle': LocalizationService.t('schedule', currentLanguage),
        'route': '/schedule',
      },
      {
        'icon': Icons.people,
        'title': LocalizationService.t('community', currentLanguage),
        'subtitle': LocalizationService.t('community', currentLanguage),
        'route': '/community',
      },
      {
        'icon': Icons.emergency,
        'title': LocalizationService.t('emergency', currentLanguage),
        'subtitle': LocalizationService.t('emergency', currentLanguage),
        'route': '/emergency',
      },
      {
        'icon': Icons.person,
        'title': LocalizationService.t('profile', currentLanguage),
        'subtitle': LocalizationService.t('profile', currentLanguage),
        'route': '/profile',
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 10),
      itemCount: menuItems.length,
      itemBuilder: (context, index) {
        final item = menuItems[index];
        final isSelected = widget.currentRoute == item['route'];
        
        return _buildMenuItem(
          icon: item['icon'] as IconData,
          title: item['title'] as String,
          subtitle: item['subtitle'] as String,
          route: item['route'] as String,
          isSelected: isSelected,
          delay: index * 50, // Staggered animation
        );
      },
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required String route,
    required bool isSelected,
    required int delay,
  }) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + delay),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset((1 - value) * 50, 0),
          child: Opacity(
            opacity: value,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
              decoration: BoxDecoration(
                color: isSelected 
                  ? Colors.white.withValues(alpha: 0.2)
                  : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
              ),
              child: ListTile(
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isSelected 
                      ? Colors.white.withValues(alpha: 0.3)
                      : Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                title: Text(
                  title,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                  ),
                ),
                subtitle: Text(
                  subtitle,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
                onTap: () {
                  if (!isSelected) {
                    context.go(route);
                  }
                  _closeSidebar();
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Container(
            height: 1,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 20),
          const Text(
            'गंगे च यमुने चैव गोदावरि सरस्वति।\nनर्मदे सिन्धु कावेरि जले अस्मिन् संनिधिं कुरु॥',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 10,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Text(
            LocalizationService.t('sacred_rivers_prayer', languageService.currentLanguageCode),
            style: const TextStyle(
              color: Colors.white60,
              fontSize: 8,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
