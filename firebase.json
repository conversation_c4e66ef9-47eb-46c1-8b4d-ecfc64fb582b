{"hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "storage": {"rules": "storage.rules"}, "emulators": {"apphosting": {"port": 5002, "rootDirectory": "./"}, "auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "database": {"port": 9000}, "pubsub": {"port": 8085}, "storage": {"port": 9199}, "dataconnect": {"port": 9399, "dataDir": "dataconnect/.dataconnect/pgliteData"}, "tasks": {"port": 9499}, "ui": {"enabled": true}, "singleProjectMode": true}}