{"hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "storage": {"rules": "storage.rules"}, "emulators": {"apphosting": {"port": 5002, "rootDirectory": "./"}, "auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8082}, "database": {"port": 9001}, "pubsub": {"port": 8086}, "storage": {"port": 9199}, "tasks": {"port": 9499}, "ui": {"enabled": true}, "singleProjectMode": true}}