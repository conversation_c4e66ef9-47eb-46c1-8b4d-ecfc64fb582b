import 'package:flutter/material.dart';
import '../constants/theme.dart';

class EmergencyScreen extends StatelessWidget {
  const EmergencyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFFFA726),
              Color(0xFFFFD700),
              Color(0xFF42A5F5),
              Color(0xFF0D47A1),
            ],
          ),
        ),
        child: Safe<PERSON>rea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 32),
              Text('Emergency Services', style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.white, fontWeight: FontWeight.bold)),
              const SizedBox(height: 24),
              // Animated SOS Button
              GestureDetector(
                onTap: () {
                  // TODO: Implement SOS action
                  showDialog(
                    context: context,
                    builder: (ctx) => AlertDialog(
                      title: const Text('SOS Sent'),
                      content: const Text('Your emergency request has been sent to the nearest help center.'),
                      actions: [TextButton(onPressed: () => Navigator.of(ctx).pop(), child: const Text('OK'))],
                    ),
                  );
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 600),
                  curve: Curves.easeInOut,
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: AppTheme.goldGradient,
                    boxShadow: [BoxShadow(color: Colors.red.withOpacity(0.3), blurRadius: 32, spreadRadius: 8)],
                  ),
                  child: Center(
                    child: Text('SOS', style: TextStyle(
                      color: Colors.red[800],
                      fontWeight: FontWeight.bold,
                      fontSize: 36,
                      letterSpacing: 2,
                      shadows: const [Shadow(color: Colors.white, blurRadius: 8)],
                    )),
                  ),
                ),
              ),
              const SizedBox(height: 32),
              // Emergency Contacts
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Emergency Contacts', style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.white, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 12),
                    _contactTile(Icons.local_police, 'Police', '100'),
                    _contactTile(Icons.local_hospital, 'Ambulance', '108'),
                    _contactTile(Icons.fire_truck, 'Fire', '101'),
                    _contactTile(Icons.support_agent, 'Helpline', '1800-123-456'),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              // Nearest Medical Facility
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [AppTheme.softShadow],
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.local_hospital, color: Colors.red[700], size: 32),
                      const SizedBox(width: 16),
                      const Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Rajahmundry Govt Hospital', style: TextStyle(fontWeight: FontWeight.bold)),
                            Text('2.1 km away • 24x7 Emergency', style: TextStyle(color: Colors.grey)),
                          ],
                        ),
                      ),
                      const Icon(Icons.directions, color: AppTheme.primaryColor),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              // Quick Help
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [AppTheme.softShadow],
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info, color: AppTheme.primaryColor),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text('For any help, approach the nearest volunteer or help desk. Stay calm and follow instructions.'),
                      ),
                    ],
                  ),
                ),
              ),
              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _contactTile(IconData icon, String label, String number) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [AppTheme.softShadow],
            ),
            child: Icon(icon, color: AppTheme.primaryColor, size: 24),
          ),
          const SizedBox(width: 14),
          Expanded(
            child: Text(label, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w600, fontSize: 16)),
          ),
          Text(number, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 16)),
        ],
      ),
    );
  }
}
