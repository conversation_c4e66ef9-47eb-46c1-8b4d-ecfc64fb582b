import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../services/weather_service.dart';
import '../services/localization_service.dart';
import '../services/language_service.dart';

class WeatherForecastScreen extends StatefulWidget {
  const WeatherForecastScreen({super.key});

  @override
  State<WeatherForecastScreen> createState() => _WeatherForecastScreenState();
}

class _WeatherForecastScreenState extends State<WeatherForecastScreen> {
  final WeatherService _weatherService = WeatherService();
  String get _selectedLanguage => languageService.currentLanguageCode;

  @override
  void initState() {
    super.initState();
    _weatherService.initialize();
  }

  @override
  void dispose() {
    _weatherService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                children: [
                  _buildCurrentWeather(),
                  _buildHourlyForecast(),
                  _buildSevenDayForecast(),
                  _buildHolyDipConditions(),
                  _buildWeatherAlerts(),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),

    );
  }

  Widget _buildHeader() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1976D2),
            Color(0xFF42A5F5),
            Color(0xFFFFB74D),
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              GestureDetector(
                onTap: () => context.pop(),
                child: const Icon(
                  Icons.arrow_back,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Text(
                  'Weather Forecast',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.location_on,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentWeather() {
    return StreamBuilder<WeatherData>(
      stream: _weatherService.currentWeatherStream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        final weather = snapshot.data!;
        return Container(
          margin: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    color: Colors.white70,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Godavari Ghats, ${weather.location}',
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 6),
                        const Text(
                          'LIVE',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${weather.temperature.round()}°C',
                              style: const TextStyle(
                                fontSize: 48,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            Text(
                              weather.description,
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                            Text(
                              'Feels like ${weather.feelsLike.round()}°C',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Updated ${_formatLastUpdated(weather.lastUpdated)}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.blue,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        Icon(
                          _getWeatherIcon(weather.condition),
                          size: 80,
                          color: _getWeatherColor(weather.condition),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildWeatherStat(
                          icon: Icons.water_drop,
                          label: 'Humidity',
                          value: '${weather.humidity}%',
                        ),
                        _buildWeatherStat(
                          icon: Icons.air,
                          label: 'Wind',
                          value: '${weather.windSpeed.round()} km/h',
                        ),
                        _buildWeatherStat(
                          icon: Icons.visibility,
                          label: 'Visibility',
                          value: '${weather.visibility} km',
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildWeatherStat(
                          icon: Icons.wb_sunny,
                          label: 'UV Index',
                          value: '${weather.uvIndex}',
                        ),
                        _buildWeatherStat(
                          icon: Icons.speed,
                          label: 'Pressure',
                          value: '${weather.pressure} mb',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildWeatherStat({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: const Color(0xFF1976D2),
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildHourlyForecast() {
    return StreamBuilder<List<HourlyForecast>>(
      stream: _weatherService.hourlyForecastStream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        final hourlyData = snapshot.data!.take(6).toList();

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Hourly Forecast',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                height: 120,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: hourlyData.length,
                  itemBuilder: (context, index) {
                    final forecast = hourlyData[index];
                    final isNow = index == 0;

                    return Container(
                      width: 80,
                      margin: const EdgeInsets.only(right: 12),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isNow ? Colors.blue.withValues(alpha: 0.1) : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isNow ? Colors.blue : Colors.grey.withValues(alpha: 0.3),
                          width: isNow ? 2 : 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Text(
                            isNow ? 'Now' : _formatHour(forecast.time),
                            style: TextStyle(
                              fontSize: 12,
                              color: isNow ? Colors.blue : Colors.grey,
                              fontWeight: isNow ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                          Icon(
                            _getWeatherIcon(forecast.condition),
                            color: _getWeatherColor(forecast.condition),
                            size: 24,
                          ),
                          Text(
                            '${forecast.temperature.round()}°',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.water_drop,
                                size: 12,
                                color: Colors.blue.withValues(alpha: 0.7),
                              ),
                              const SizedBox(width: 2),
                              Text(
                                '${forecast.chanceOfRain}%',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.blue.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHourlyItem(String temp, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: icon == Icons.wb_sunny ? Colors.orange : 
                 icon == Icons.cloud ? Colors.grey : Colors.blue,
          size: 24,
        ),
        const SizedBox(height: 4),
        Text(
          temp,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildSevenDayForecast() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '7-Day Forecast',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildDayForecast('Today', Icons.wb_sunny, 'Partly Cloudy', '20%', '32°/24°'),
                const Divider(height: 24),
                _buildDayForecast('Mon', Icons.grain, 'Heavy Rain', '85%', '27°/22°'),
                const Divider(height: 24),
                _buildDayForecast('Tue', Icons.cloud, 'Cloudy', '40%', '29°/23°'),
                const Divider(height: 24),
                _buildDayForecast('Wed', Icons.wb_sunny, 'Sunny', '5%', '33°/25°'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDayForecast(String day, IconData icon, String condition, String rain, String temp) {
    return Row(
      children: [
        SizedBox(
          width: 50,
          child: Text(
            day,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Icon(
          icon,
          color: icon == Icons.wb_sunny ? Colors.orange :
                 icon == Icons.cloud ? Colors.grey : Colors.blue,
          size: 24,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                condition,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              Text(
                rain,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        Text(
          temp,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildHolyDipConditions() {
    return StreamBuilder<WeatherData>(
      stream: _weatherService.currentWeatherStream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        final weather = snapshot.data!;
        final isGoodForDip = _weatherService.isGoodForHolyDip();
        final recommendation = _weatherService.getHolyDipRecommendation();

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Holy Dip Conditions',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),

              // Main recommendation card
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: isGoodForDip
                      ? [Colors.green.withValues(alpha: 0.8), Colors.green.withValues(alpha: 0.6)]
                      : [Colors.orange.withValues(alpha: 0.8), Colors.red.withValues(alpha: 0.6)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          isGoodForDip ? Icons.check_circle : Icons.warning,
                          color: Colors.white,
                          size: 32,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            isGoodForDip ? 'EXCELLENT CONDITIONS' : 'CAUTION ADVISED',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      recommendation,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildConditionCard(
                      icon: Icons.thermostat,
                      color: _getTemperatureColor(weather.temperature),
                      title: 'Temperature',
                      subtitle: '${weather.temperature.round()}°C\nFeels ${weather.feelsLike.round()}°C',
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildConditionCard(
                      icon: Icons.air,
                      color: _getWindColor(weather.windSpeed),
                      title: 'Wind Speed',
                      subtitle: '${weather.windSpeed.round()} km/h\n${weather.windDirection}',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildConditionCard(
                      icon: Icons.wb_sunny,
                      color: _getUVColor(weather.uvIndex),
                      title: 'UV Index',
                      subtitle: '${weather.uvIndex}\n${_getUVDescription(weather.uvIndex)}',
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildConditionCard(
                      icon: Icons.visibility,
                      color: _getVisibilityColor(weather.visibility),
                      title: 'Visibility',
                      subtitle: '${weather.visibility} km\n${_getVisibilityDescription(weather.visibility)}',
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildConditionCard({
    required IconData icon,
    required Color color,
    required String title,
    required String subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeatherAlerts() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Weather Alerts',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          _buildAlertCard(
            icon: Icons.warning,
            color: Colors.red,
            title: 'Heavy Rain Alert',
            subtitle: 'Expected from 3:00 PM to 6:00 PM today. Avoid river activities.',
            isWarning: true,
          ),
          const SizedBox(height: 12),
          _buildAlertCard(
            icon: Icons.air,
            color: Colors.orange,
            title: 'Air Quality Index',
            subtitle: 'Moderate (AQI: 78). Sensitive individuals should limit outdoor activities.',
            isWarning: false,
          ),
          const SizedBox(height: 12),
          _buildAlertCard(
            icon: Icons.wb_sunny,
            color: Colors.orange,
            title: 'UV Index High',
            subtitle: 'UV Index 7. Use sunscreen and seek shade during 11 AM - 4 PM.',
            isWarning: false,
          ),
        ],
      ),
    );
  }

  Widget _buildAlertCard({
    required IconData icon,
    required Color color,
    required String title,
    required String subtitle,
    required bool isWarning,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isWarning ? color.withValues(alpha: 0.1) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: isWarning ? Border.all(color: color.withValues(alpha: 0.3)) : null,
        boxShadow: !isWarning ? [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isWarning ? color : Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(Icons.home, 'Home', false),
              _buildNavItem(Icons.chat, 'Chats', false),
              _buildNavItem(Icons.cloud, 'Weather', true),
              _buildNavItem(Icons.schedule, 'Schedule', false),
              _buildNavItem(Icons.person, 'Profile', false),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, bool isActive) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          color: isActive ? const Color(0xFF1976D2) : Colors.grey,
          size: 24,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: isActive ? const Color(0xFF1976D2) : Colors.grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  // Helper methods for weather display
  IconData _getWeatherIcon(WeatherCondition condition) {
    switch (condition) {
      case WeatherCondition.sunny:
        return Icons.wb_sunny;
      case WeatherCondition.partlyCloudy:
        return Icons.wb_cloudy;
      case WeatherCondition.cloudy:
        return Icons.cloud;
      case WeatherCondition.rainy:
        return Icons.grain;
      case WeatherCondition.thunderstorm:
        return Icons.thunderstorm;
      case WeatherCondition.foggy:
        return Icons.foggy;
    }
  }

  Color _getWeatherColor(WeatherCondition condition) {
    switch (condition) {
      case WeatherCondition.sunny:
        return Colors.orange;
      case WeatherCondition.partlyCloudy:
        return Colors.blue;
      case WeatherCondition.cloudy:
        return Colors.grey;
      case WeatherCondition.rainy:
        return Colors.blue;
      case WeatherCondition.thunderstorm:
        return Colors.purple;
      case WeatherCondition.foggy:
        return Colors.grey;
    }
  }

  List<Color> _getWeatherGradient(WeatherCondition condition) {
    switch (condition) {
      case WeatherCondition.sunny:
        return [const Color(0xFFFFB74D), const Color(0xFFFF9800)];
      case WeatherCondition.partlyCloudy:
        return [const Color(0xFF42A5F5), const Color(0xFF1976D2)];
      case WeatherCondition.cloudy:
        return [const Color(0xFF90A4AE), const Color(0xFF607D8B)];
      case WeatherCondition.rainy:
        return [const Color(0xFF5C6BC0), const Color(0xFF3F51B5)];
      case WeatherCondition.thunderstorm:
        return [const Color(0xFF7E57C2), const Color(0xFF512DA8)];
      case WeatherCondition.foggy:
        return [const Color(0xFF78909C), const Color(0xFF546E7A)];
    }
  }

  String _formatLastUpdated(DateTime lastUpdated) {
    final now = DateTime.now();
    final diff = now.difference(lastUpdated);

    if (diff.inMinutes < 1) {
      return 'just now';
    } else if (diff.inMinutes < 60) {
      return '${diff.inMinutes}m ago';
    } else if (diff.inHours < 24) {
      return '${diff.inHours}h ago';
    } else {
      return '${diff.inDays}d ago';
    }
  }

  String _formatHour(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  // Helper methods for holy dip conditions
  Color _getTemperatureColor(double temp) {
    if (temp < 20) return Colors.blue;
    if (temp < 30) return Colors.green;
    if (temp < 35) return Colors.orange;
    return Colors.red;
  }

  Color _getWindColor(double windSpeed) {
    if (windSpeed < 10) return Colors.green;
    if (windSpeed < 20) return Colors.orange;
    return Colors.red;
  }

  Color _getUVColor(int uvIndex) {
    if (uvIndex < 3) return Colors.green;
    if (uvIndex < 6) return Colors.yellow;
    if (uvIndex < 8) return Colors.orange;
    return Colors.red;
  }

  Color _getVisibilityColor(int visibility) {
    if (visibility >= 10) return Colors.green;
    if (visibility >= 5) return Colors.orange;
    return Colors.red;
  }

  String _getUVDescription(int uvIndex) {
    if (uvIndex < 3) return 'Low';
    if (uvIndex < 6) return 'Moderate';
    if (uvIndex < 8) return 'High';
    return 'Very High';
  }

  String _getVisibilityDescription(int visibility) {
    if (visibility >= 10) return 'Excellent';
    if (visibility >= 5) return 'Good';
    return 'Poor';
  }
}
