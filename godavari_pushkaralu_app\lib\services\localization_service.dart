class LocalizationService {
  static const Map<String, Map<String, String>> _translations = {
    // Common
    'welcome': {
      'en': 'Welcome',
      'hi': 'स्वागत',
      'te': 'స్వాగతం',
      'ta': 'வரவேற்கிறோம்',
    },
    'continue': {
      'en': 'Continue',
      'hi': 'जारी रखें',
      'te': 'కొనసాగించు',
      'ta': 'தொடரவும்',
    },
    'next': {
      'en': 'Next',
      'hi': 'अगला',
      'te': 'తదుపరి',
      'ta': 'அடுத்து',
    },
    'back': {
      'en': 'Back',
      'hi': 'वापस',
      'te': 'వెనుకకు',
      'ta': 'பின்னால்',
    },
    'done': {
      'en': 'Done',
      'hi': 'पूर्ण',
      'te': 'పూర్తి',
      'ta': 'முடிந்தது',
    },
    'close': {
      'en': 'Close',
      'hi': 'बंद करें',
      'te': 'మూసివేయి',
      'ta': 'மூடு',
    },
    
    // Onboarding
    'choose_language': {
      'en': 'Choose Your Language',
      'hi': 'अपनी भाषा चुनें',
      'te': 'మీ భాషను ఎంచుకోండి',
      'ta': 'உங்கள் மொழியைத் தேர்ந்தெடுக்கவும்',
    },
    'language_subtitle': {
      'en': 'Select your preferred language for the best experience',
      'hi': 'सर्वोत्तम अनुभव के लिए अपनी पसंदीदा भाषा चुनें',
      'te': 'ఉత్తమ అనుభవం కోసం మీ ఇష్టమైన భాషను ఎంచుకోండి',
      'ta': 'சிறந்த அனுபவத்திற்காக உங்கள் விருப்பமான மொழியைத் தேர்ந்தெடுக்கவும்',
    },
    'godavari_pushkaralu': {
      'en': 'Godavari Pushkaralu',
      'hi': 'गोदावरी पुष्करालु',
      'te': 'గోదావరి పుష్కరాలు',
      'ta': 'கோதாவரி புஷ்கராலு',
    },
    'sacred_river_festival': {
      'en': 'Sacred River Festival 2027',
      'hi': 'पवित्र नदी महोत्सव 2027',
      'te': 'పవిత్ర నది మహోత్సవం 2027',
      'ta': 'புனித நதி திருவிழா 2027',
    },
    
    // Navigation
    'home': {
      'en': 'Home',
      'hi': 'मुख्य पृष्ठ',
      'te': 'హోమ్',
      'ta': 'முகப்பு',
    },
    'stay_food': {
      'en': 'Stay & Food',
      'hi': 'आवास और भोजन',
      'te': 'బస మరియు ఆహారం',
      'ta': 'தங்குமிடம் மற்றும் உணவு',
    },
    'transportation': {
      'en': 'Transportation',
      'hi': 'परिवहन',
      'te': 'రవాణా',
      'ta': 'போக்குவரத்து',
    },
    'medical_services': {
      'en': 'Medical Services',
      'hi': 'चिकित्सा सेवाएं',
      'te': 'వైద్య సేవలు',
      'ta': 'மருத்துவ சேவைகள்',
    },
    'weather_forecast': {
      'en': 'Weather Forecast',
      'hi': 'मौसम पूर्वानुमान',
      'te': 'వాతావరణ సూచన',
      'ta': 'வானிலை முன்னறிவிப்பு',
    },
    'schedule': {
      'en': 'Schedule',
      'hi': 'कार्यक्रम',
      'te': 'షెడ్యూల్',
      'ta': 'அட்டவணை',
    },
    'community': {
      'en': 'Community',
      'hi': 'समुदाय',
      'te': 'కమ్యూనిటీ',
      'ta': 'சமூகம்',
    },
    'emergency': {
      'en': 'Emergency',
      'hi': 'आपातकाल',
      'te': 'అత్యవసరం',
      'ta': 'அவசரநிலை',
    },
    'profile': {
      'en': 'Profile',
      'hi': 'प्रोफ़ाइल',
      'te': 'ప్రొఫైల్',
      'ta': 'சுயவிவரம்',
    },
    
    // Home Screen
    'quick_actions': {
      'en': 'Quick Actions',
      'hi': 'त्वरित कार्य',
      'te': 'త్వరిత చర్యలు',
      'ta': 'விரைவு செயல்கள்',
    },
    'holy_dip': {
      'en': 'Holy Dip',
      'hi': 'पवित्र स्नान',
      'te': 'పవిత్ర స్నానం',
      'ta': 'புனித நீராடல்',
    },
    'plan_sacred_bath': {
      'en': 'Plan sacred bath',
      'hi': 'पवित्र स्नान की योजना',
      'te': 'పవిత్ర స్నానం ప్లాన్ చేయండి',
      'ta': 'புனித குளியல் திட்டம்',
    },
    'book_puja': {
      'en': 'Book Puja',
      'hi': 'पूजा बुक करें',
      'te': 'పూజ బుక్ చేయండి',
      'ta': 'பூஜை முன்பதிவு',
    },
    'temple_rituals': {
      'en': 'Temple rituals',
      'hi': 'मंदिर अनुष्ठान',
      'te': 'దేవాలయ కర్మలు',
      'ta': 'கோயில் சடங்குகள்',
    },
    'queue_status': {
      'en': 'Queue Status',
      'hi': 'कतार की स्थिति',
      'te': 'క్యూ స్థితి',
      'ta': 'வரிசை நிலை',
    },
    'live_updates': {
      'en': 'Live updates',
      'hi': 'लाइव अपडेट',
      'te': 'లైవ్ అప్‌డేట్‌లు',
      'ta': 'நேரடி புதுப்பிப்புகள்',
    },
    'medical': {
      'en': 'Medical',
      'hi': 'चिकित्सा',
      'te': 'వైద్యం',
      'ta': 'மருத்துவம்',
    },
    'health_services': {
      'en': 'Health services',
      'hi': 'स्वास्थ्य सेवाएं',
      'te': 'ఆరోగ్య సేవలు',
      'ta': 'சுகாதார சேவைகள்',
    },
    'weather': {
      'en': 'Weather',
      'hi': 'मौसम',
      'te': 'వాతావరణం',
      'ta': 'வானிலை',
    },
    'forecast_alerts': {
      'en': 'Forecast & alerts',
      'hi': 'पूर्वानुमान और अलर्ट',
      'te': 'సూచన మరియు హెచ్చరికలు',
      'ta': 'முன்னறிவிப்பு மற்றும் எச்சரிக்கைகள்',
    },
    
    // Today's Schedule
    'todays_schedule': {
      'en': 'Today\'s Schedule',
      'hi': 'आज का कार्यक्रम',
      'te': 'నేటి షెడ్యూల్',
      'ta': 'இன்றைய அட்டவணை',
    },
    'view_all': {
      'en': 'View All',
      'hi': 'सभी देखें',
      'te': 'అన్నీ చూడండి',
      'ta': 'அனைத்தையும் பார்க்கவும்',
    },
    'morning_aarti': {
      'en': 'Morning Aarti',
      'hi': 'प्रातःकालीन आरती',
      'te': 'ఉదయ ఆరతి',
      'ta': 'காலை ஆரத்தி',
    },
    'all_temples': {
      'en': 'All Temples',
      'hi': 'सभी मंदिर',
      'te': 'అన్ని దేవాలయాలు',
      'ta': 'அனைத்து கோயில்கள்',
    },
    'sacred_bath_time': {
      'en': 'Sacred Bath Time',
      'hi': 'पवित्र स्नान समय',
      'te': 'పవిత్ర స్నాన సమయం',
      'ta': 'புனித குளியல் நேரம்',
    },
    'live': {
      'en': 'Live',
      'hi': 'लाइव',
      'te': 'లైవ్',
      'ta': 'நேரடி',
    },
    'optional': {
      'en': 'Optional',
      'hi': 'वैकल्पिक',
      'te': 'ఐచ్ఛికం',
      'ta': 'விருப்பமான',
    },
    
    // Sacred Prayer
    'sacred_rivers_prayer': {
      'en': 'Sacred Rivers Prayer',
      'hi': 'पवित्र नदियों की प्रार्थना',
      'te': 'పవిత్ర నదుల ప్రార్థన',
      'ta': 'புனித நதிகளின் பிரார்த்தனை',
    },
  };
  
  static String translate(String key, String languageCode) {
    final translations = _translations[key];
    if (translations == null) return key;
    
    return translations[languageCode] ?? translations['en'] ?? key;
  }
  
  static String t(String key, String languageCode) {
    return translate(key, languageCode);
  }
}
