class LocalizationService {
  static const Map<String, Map<String, String>> _translations = {
    // Common
    'welcome': {
      'en': 'Welcome',
      'hi': 'स्वागत',
      'te': 'స్వాగతం',
      'ta': 'வரவேற்கிறோம்',
    },
    'continue': {
      'en': 'Continue',
      'hi': 'जारी रखें',
      'te': 'కొనసాగించు',
      'ta': 'தொடரவும்',
    },
    'next': {
      'en': 'Next',
      'hi': 'अगला',
      'te': 'తదుపరి',
      'ta': 'அடுத்து',
    },
    'back': {
      'en': 'Back',
      'hi': 'वापस',
      'te': 'వెనుకకు',
      'ta': 'பின்னால்',
    },
    'done': {
      'en': 'Done',
      'hi': 'पूर्ण',
      'te': 'పూర్తి',
      'ta': 'முடிந்தது',
    },
    'skip': {
      'en': 'Skip',
      'hi': 'छोड़ें',
      'te': 'దాటవేయి',
      'ta': 'தவிர்க்கவும்',
    },
    'tap_anywhere': {
      'en': 'Tap anywhere to continue',
      'hi': 'जारी रखने के लिए कहीं भी टैप करें',
      'te': 'కొనసాగించడానికి ఎక్కడైనా నొక్కండి',
      'ta': 'தொடர எங்கும் தட்டவும்',
    },
    'close': {
      'en': 'Close',
      'hi': 'बंद करें',
      'te': 'మూసివేయి',
      'ta': 'மூடு',
    },
    
    // Onboarding
    'choose_language': {
      'en': 'Choose Your Language',
      'hi': 'अपनी भाषा चुनें',
      'te': 'మీ భాషను ఎంచుకోండి',
      'ta': 'உங்கள் மொழியைத் தேர்ந்தெடுக்கவும்',
    },
    'language_subtitle': {
      'en': 'Select your preferred language for the best experience',
      'hi': 'सर्वोत्तम अनुभव के लिए अपनी पसंदीदा भाषा चुनें',
      'te': 'ఉత్తమ అనుభవం కోసం మీ ఇష్టమైన భాషను ఎంచుకోండి',
      'ta': 'சிறந்த அனுபவத்திற்காக உங்கள் விருப்பமான மொழியைத் தேர்ந்தெடுக்கவும்',
    },
    'godavari_pushkaralu': {
      'en': 'Godavari Pushkaralu',
      'hi': 'गोदावरी पुष्करालु',
      'te': 'గోదావరి పుష్కరాలు',
      'ta': 'கோதாவரி புஷ்கராலு',
    },
    'sacred_river_festival': {
      'en': 'Sacred River Festival 2027',
      'hi': 'पवित्र नदी महोत्सव 2027',
      'te': 'పవిత్ర నది మహోత్సవం 2027',
      'ta': 'புனித நதி திருவிழா 2027',
    },
    
    // Navigation
    'home': {
      'en': 'Home',
      'hi': 'मुख्य पृष्ठ',
      'te': 'హోమ్',
      'ta': 'முகப்பு',
    },
    'stay_food': {
      'en': 'Stay & Food',
      'hi': 'आवास और भोजन',
      'te': 'బస మరియు ఆహారం',
      'ta': 'தங்குமிடம் மற்றும் உணவு',
    },
    'transportation': {
      'en': 'Transportation',
      'hi': 'परिवहन',
      'te': 'రవాణా',
      'ta': 'போக்குவரத்து',
    },
    'medical_services': {
      'en': 'Medical Services',
      'hi': 'चिकित्सा सेवाएं',
      'te': 'వైద్య సేవలు',
      'ta': 'மருத்துவ சேவைகள்',
    },
    'weather_forecast': {
      'en': 'Weather Forecast',
      'hi': 'मौसम पूर्वानुमान',
      'te': 'వాతావరణ సూచన',
      'ta': 'வானிலை முன்னறிவிப்பு',
    },
    'schedule': {
      'en': 'Schedule',
      'hi': 'कार्यक्रम',
      'te': 'షెడ్యూల్',
      'ta': 'அட்டவணை',
    },
    'community': {
      'en': 'Community',
      'hi': 'समुदाय',
      'te': 'కమ్యూనిటీ',
      'ta': 'சமூகம்',
    },
    'emergency': {
      'en': 'Emergency',
      'hi': 'आपातकाल',
      'te': 'అత్యవసరం',
      'ta': 'அவசரநிலை',
    },
    'profile': {
      'en': 'Profile',
      'hi': 'प्रोफ़ाइल',
      'te': 'ప్రొఫైల్',
      'ta': 'சுயவிவரம்',
    },
    
    // Home Screen
    'quick_actions': {
      'en': 'Quick Actions',
      'hi': 'त्वरित कार्य',
      'te': 'త్వరిత చర్యలు',
      'ta': 'விரைவு செயல்கள்',
    },
    'holy_dip': {
      'en': 'Holy Dip',
      'hi': 'पवित्र स्नान',
      'te': 'పవిత్ర స్నానం',
      'ta': 'புனித நீராடல்',
    },
    'plan_sacred_bath': {
      'en': 'Plan sacred bath',
      'hi': 'पवित्र स्नान की योजना',
      'te': 'పవిత్ర స్నానం ప్లాన్ చేయండి',
      'ta': 'புனித குளியல் திட்டம்',
    },
    'book_puja': {
      'en': 'Book Puja',
      'hi': 'पूजा बुक करें',
      'te': 'పూజ బుక్ చేయండి',
      'ta': 'பூஜை முன்பதிவு',
    },
    'temple_rituals': {
      'en': 'Temple rituals',
      'hi': 'मंदिर अनुष्ठान',
      'te': 'దేవాలయ కర్మలు',
      'ta': 'கோயில் சடங்குகள்',
    },
    'queue_status': {
      'en': 'Queue Status',
      'hi': 'कतार की स्थिति',
      'te': 'క్యూ స్థితి',
      'ta': 'வரிசை நிலை',
    },
    'live_updates': {
      'en': 'Live updates',
      'hi': 'लाइव अपडेट',
      'te': 'లైవ్ అప్‌డేట్‌లు',
      'ta': 'நேரடி புதுப்பிப்புகள்',
    },
    'medical': {
      'en': 'Medical',
      'hi': 'चिकित्सा',
      'te': 'వైద్యం',
      'ta': 'மருத்துவம்',
    },
    'health_services': {
      'en': 'Health services',
      'hi': 'स्वास्थ्य सेवाएं',
      'te': 'ఆరోగ్య సేవలు',
      'ta': 'சுகாதார சேவைகள்',
    },
    'weather': {
      'en': 'Weather',
      'hi': 'मौसम',
      'te': 'వాతావరణం',
      'ta': 'வானிலை',
    },
    'forecast_alerts': {
      'en': 'Forecast & alerts',
      'hi': 'पूर्वानुमान और अलर्ट',
      'te': 'సూచన మరియు హెచ్చరికలు',
      'ta': 'முன்னறிவிப்பு மற்றும் எச்சரிக்கைகள்',
    },
    
    // Today's Schedule
    'todays_schedule': {
      'en': 'Today\'s Schedule',
      'hi': 'आज का कार्यक्रम',
      'te': 'నేటి షెడ్యూల్',
      'ta': 'இன்றைய அட்டவணை',
    },
    'view_all': {
      'en': 'View All',
      'hi': 'सभी देखें',
      'te': 'అన్నీ చూడండి',
      'ta': 'அனைத்தையும் பார்க்கவும்',
    },
    'morning_aarti': {
      'en': 'Morning Aarti',
      'hi': 'प्रातःकालीन आरती',
      'te': 'ఉదయ ఆరతి',
      'ta': 'காலை ஆரத்தி',
    },
    'all_temples': {
      'en': 'All Temples',
      'hi': 'सभी मंदिर',
      'te': 'అన్ని దేవాలయాలు',
      'ta': 'அனைத்து கோயில்கள்',
    },
    'sacred_bath_time': {
      'en': 'Sacred Bath Time',
      'hi': 'पवित्र स्नान समय',
      'te': 'పవిత్ర స్నాన సమయం',
      'ta': 'புனித குளியல் நேரம்',
    },
    'live': {
      'en': 'Live',
      'hi': 'लाइव',
      'te': 'లైవ్',
      'ta': 'நேரடி',
    },
    'optional': {
      'en': 'Optional',
      'hi': 'वैकल्पिक',
      'te': 'ఐచ్ఛికం',
      'ta': 'விருப்பமான',
    },
    
    // Sacred Prayer
    'sacred_rivers_prayer': {
      'en': 'Sacred Rivers Prayer',
      'hi': 'पवित्र नदियों की प्रार्थना',
      'te': 'పవిత్ర నదుల ప్రార్థన',
      'ta': 'புனித நதிகளின் பிரார்த்தனை',
    },

    // New Onboarding Pages
    'spiritual_journey': {
      'en': 'Your Spiritual Journey Begins',
      'hi': 'आपकी आध्यात्मिक यात्रा शुरू होती है',
      'te': 'మీ ఆధ్యాత్మిక యాత్ర ప్రారంభమవుతుంది',
      'ta': 'உங்கள் ஆன்மீக பயணம் தொடங்குகிறது',
    },
    'spiritual_journey_desc': {
      'en': 'Experience the divine blessings of River Godavari with our comprehensive guide and premium services.',
      'hi': 'हमारे व्यापक गाइड और प्रीमियम सेवाओं के साथ नदी गोदावरी के दिव्य आशीर्वाद का अनुभव करें।',
      'te': 'మా సమగ్ర గైడ్ మరియు ప్రీమియం సేవలతో గోదావరి నది యొక్క దైవిక ఆశీర్వాదాలను అనుభవించండి.',
      'ta': 'எங்கள் விரிவான வழிகாட்டி மற்றும் பிரீமியம் சேவைகளுடன் கோதாவரி நதியின் தெய்வீக ஆசீர்வாதங்களை அனுபவிக்கவும்.',
    },
    'plan_your_visit': {
      'en': 'Plan Your Sacred Visit',
      'hi': 'अपनी पवित्र यात्रा की योजना बनाएं',
      'te': 'మీ పవిత్ర సందర్శనను ప్లాన్ చేయండి',
      'ta': 'உங்கள் புனித வருகையைத் திட்டமிடுங்கள்',
    },
    'plan_visit_desc': {
      'en': 'Book accommodations, transportation, and spiritual services. Get real-time updates and queue status.',
      'hi': 'आवास, परिवहन और आध्यात्मिक सेवाओं को बुक करें। रियल-टाइम अपडेट और कतार की स्थिति प्राप्त करें।',
      'te': 'వసతి, రవాణా మరియు ఆధ్యాత్మిక సేవలను బుక్ చేయండి. రియల్ టైమ్ అప్‌డేట్‌లు మరియు క్యూ స్థితిని పొందండి.',
      'ta': 'தங்குமிடம், போக்குவரத்து மற்றும் ஆன்மீக சேவைகளை முன்பதிவு செய்யுங்கள். நிகழ்நேர புதுப்பிப்புகள் மற்றும் வரிசை நிலையைப் பெறுங்கள்.',
    },
    'stay_connected': {
      'en': 'Stay Connected & Safe',
      'hi': 'जुड़े रहें और सुरक्षित रहें',
      'te': 'కనెక్ట్ అయి ఉండండి మరియు సురక్షితంగా ఉండండి',
      'ta': 'இணைந்திருங்கள் மற்றும் பாதுகாப்பாக இருங்கள்',
    },
    'stay_connected_desc': {
      'en': 'Access emergency services, weather alerts, and connect with fellow devotees in our community.',
      'hi': 'आपातकालीन सेवाओं, मौसम अलर्ट तक पहुंचें और हमारे समुदाय में साथी भक्तों से जुड़ें।',
      'te': 'అత్యవసర సేవలు, వాతావరణ హెచ్చరికలను యాక్సెస్ చేయండి మరియు మా కమ్యూనిటీలో తోటి భక్తులతో కనెక్ట్ అవ్వండి.',
      'ta': 'அவசர சேவைகள், வானிலை எச்சரிக்கைகளை அணுகவும் மற்றும் எங்கள் சமூகத்தில் சக பக்தர்களுடன் இணைக்கவும்.',
    },
    'premium_experience': {
      'en': 'Premium Spiritual Experience',
      'hi': 'प्रीमियम आध्यात्मिक अनुभव',
      'te': 'ప్రీమియం ఆధ్యాత్మిక అనుభవం',
      'ta': 'பிரீமியம் ஆன்மீக அனுபவம்',
    },
    'premium_desc': {
      'en': 'Daily puja bookings, mantras, VIP darshan, and exclusive spiritual services for a blessed festival.',
      'hi': 'दैनिक पूजा बुकिंग, मंत्र, वीआईपी दर्शन, और धन्य त्योहार के लिए विशेष आध्यात्मिक सेवाएं।',
      'te': 'రోజువారీ పూజా బుకింగ్‌లు, మంత్రాలు, వీఐపీ దర్శనం మరియు ఆశీర్వాద పండుగ కోసం ప్రత్యేక ఆధ్యాత్మిక సేవలు.',
      'ta': 'தினசரி பூஜை முன்பதிவுகள், மந்திரங்கள், விஐபி தரிசனம் மற்றும் ஆசீர்வதிக்கப்பட்ட திருவிழாவிற்கான பிரத்யேக ஆன்மீக சேவைகள்.',
    },
    'get_started': {
      'en': 'Get Started',
      'hi': 'शुरू करें',
      'te': 'ప్రారంభించండి',
      'ta': 'தொடங்குங்கள்',
    },

    // Services Screen
    'services': {
      'en': 'Services',
      'hi': 'सेवाएं',
      'te': 'సేవలు',
      'ta': 'சேவைகள்',
    },
    'spiritual_services': {
      'en': 'Spiritual Services',
      'hi': 'आध्यात्मिक सेवाएं',
      'te': 'ఆధ్యాత్మిక సేవలు',
      'ta': 'ஆன்மீக சேவைகள்',
    },
    'book_spiritual_services': {
      'en': 'Book your spiritual journey and premium services',
      'hi': 'अपनी आध्यात्मिक यात्रा और प्रीमियम सेवाओं को बुक करें',
      'te': 'మీ ఆధ్యాత్మిక యాత్ర మరియు ప్రీమియం సేవలను బుక్ చేయండి',
      'ta': 'உங்கள் ஆன்மீக பயணம் மற்றும் பிரீமியம் சேவைகளை முன்பதிவு செய்யுங்கள்',
    },
    'puja_services': {
      'en': 'Puja Services',
      'hi': 'पूजा सेवाएं',
      'te': 'పూజా సేవలు',
      'ta': 'பூஜை சேவைகள்',
    },
    'daily_puja': {
      'en': 'Daily Puja',
      'hi': 'दैनिक पूजा',
      'te': 'రోజువారీ పూజ',
      'ta': 'தினசரி பூஜை',
    },
    'daily_puja_desc': {
      'en': 'Morning and evening prayers with aarti',
      'hi': 'आरती के साथ सुबह और शाम की प्रार्थना',
      'te': 'ఆరతితో ఉదయం మరియు సాయంత్రం ప్రార్థనలు',
      'ta': 'ஆரத்தியுடன் காலை மற்றும் மாலை பிரார்த்தனைகள்',
    },
    'special_puja': {
      'en': 'Special Puja',
      'hi': 'विशेष पूजा',
      'te': 'ప్రత్యేక పూజ',
      'ta': 'சிறப்பு பூஜை',
    },
    'special_puja_desc': {
      'en': 'Customized puja for specific wishes',
      'hi': 'विशिष्ट इच्छाओं के लिए अनुकूलित पूजा',
      'te': 'నిర్దిష్ట కోరికల కోసం అనుకూలీకృత పూజ',
      'ta': 'குறிப்பிட்ட விருப்பங்களுக்கான தனிப்பயன் பூஜை',
    },
    'homam_service': {
      'en': 'Homam Service',
      'hi': 'होम सेवा',
      'te': 'హోమం సేవ',
      'ta': 'ஹோம சேவை',
    },
    'homam_desc': {
      'en': 'Sacred fire ritual for prosperity',
      'hi': 'समृद्धि के लिए पवित्र अग्नि अनुष्ठान',
      'te': 'శ్రేయస్సు కోసం పవిత్ర అగ్ని కర్మ',
      'ta': 'செழிப்புக்கான புனித நெருப்பு சடங்கு',
    },
    'darshan_services': {
      'en': 'Darshan Services',
      'hi': 'दर्शन सेवाएं',
      'te': 'దర్శన సేవలు',
      'ta': 'தரிசன சேவைகள்',
    },
    'vip_darshan': {
      'en': 'VIP Darshan',
      'hi': 'वीआईपी दर्शन',
      'te': 'వీఐపీ దర్శనం',
      'ta': 'விஐபி தரிசனம்',
    },
    'vip_darshan_desc': {
      'en': 'Priority access with minimal waiting',
      'hi': 'न्यूनतम प्रतीक्षा के साथ प्राथमिकता पहुंच',
      'te': 'కనిష్ట వేచి ఉండటంతో ప్రాధాన్యత యాక్సెస్',
      'ta': 'குறைந்த காத்திருப்புடன் முன்னுரிமை அணுகல்',
    },
    'special_darshan': {
      'en': 'Special Darshan',
      'hi': 'विशेष दर्शन',
      'te': 'ప్రత్యేక దర్శనం',
      'ta': 'சிறப்பு தரிசனம்',
    },
    'special_darshan_desc': {
      'en': 'Scheduled darshan at specific times',
      'hi': 'विशिष्ट समय पर निर्धारित दर्शन',
      'te': 'నిర్దిష్ట సమయాల్లో షెడ్యూల్ చేయబడిన దర్శనం',
      'ta': 'குறிப்பிட்ட நேரங்களில் திட்டமிடப்பட்ட தரிசனம்',
    },
    'accommodation': {
      'en': 'Accommodation',
      'hi': 'आवास',
      'te': 'వసతి',
      'ta': 'தங்குமிடம்',
    },
    'dharamshala': {
      'en': 'Dharamshala',
      'hi': 'धर्मशाला',
      'te': 'ధర్మశాల',
      'ta': 'தர்மசாலா',
    },
    'dharamshala_desc': {
      'en': 'Basic accommodation for pilgrims',
      'hi': 'तीर्थयात्रियों के लिए बुनियादी आवास',
      'te': 'యాత్రికుల కోసం ప్రాథమిక వసతి',
      'ta': 'யாத்ரீகர்களுக்கான அடிப்படை தங்குமிடம்',
    },
    'guest_house': {
      'en': 'Guest House',
      'hi': 'गेस्ट हाउस',
      'te': 'గెస్ట్ హౌస్',
      'ta': 'விருந்தினர் இல்லம்',
    },
    'guest_house_desc': {
      'en': 'Comfortable rooms with modern amenities',
      'hi': 'आधुनिक सुविधाओं के साथ आरामदायक कमरे',
      'te': 'ఆధునిక సౌకర్యాలతో సౌకర్యవంతమైన గదులు',
      'ta': 'நவீன வசதிகளுடன் வசதியான அறைகள்',
    },
    'shuttle_service': {
      'en': 'Shuttle Service',
      'hi': 'शटल सेवा',
      'te': 'షటిల్ సేవ',
      'ta': 'ஷட்டில் சேவை',
    },
    'shuttle_desc': {
      'en': 'Regular bus service to main ghats',
      'hi': 'मुख्य घाटों के लिए नियमित बस सेवा',
      'te': 'ప్రధాన ఘాట్లకు రెగ్యులర్ బస్ సేవ',
      'ta': 'முக்கிய காட்களுக்கு வழக்கமான பேருந்து சேவை',
    },
    'private_vehicle': {
      'en': 'Private Vehicle',
      'hi': 'निजी वाहन',
      'te': 'ప్రైవేట్ వాహనం',
      'ta': 'தனியார் வாகனம்',
    },
    'private_vehicle_desc': {
      'en': 'Dedicated vehicle with driver',
      'hi': 'ड्राइवर के साथ समर्पित वाहन',
      'te': 'డ్రైవర్‌తో అంకితమైన వాహనం',
      'ta': 'ஓட்டுநருடன் அர்ப்பணிக்கப்பட்ட வாகனம்',
    },
    'emergency_services': {
      'en': 'Emergency Services',
      'hi': 'आपातकालीन सेवाएं',
      'te': 'అత్యవసర సేవలు',
      'ta': 'அவசர சேவைகள்',
    },
    'emergency_desc': {
      'en': '24/7 medical and safety support',
      'hi': '24/7 चिकित्सा और सुरक्षा सहायता',
      'te': '24/7 వైద్య మరియు భద్రతా మద్దతు',
      'ta': '24/7 மருத்துவ மற்றும் பாதுகாப்பு ஆதரவு',
    },
    'call_now': {
      'en': 'Call Now',
      'hi': 'अभी कॉल करें',
      'te': 'ఇప్పుడే కాల్ చేయండి',
      'ta': 'இப்போது அழைக்கவும்',
    },
    'book': {
      'en': 'Book',
      'hi': 'बुक करें',
      'te': 'బుక్ చేయండి',
      'ta': 'முன்பதிவு',
    },
    'book_service': {
      'en': 'Book Service',
      'hi': 'सेवा बुक करें',
      'te': 'సేవను బుక్ చేయండి',
      'ta': 'சேவையை முன்பதிவு செய்யுங்கள்',
    },
    'confirm_booking': {
      'en': 'Do you want to book',
      'hi': 'क्या आप बुक करना चाहते हैं',
      'te': 'మీరు బుక్ చేయాలనుకుంటున్నారా',
      'ta': 'நீங்கள் முன்பதிவு செய்ய விரும்புகிறீர்களா',
    },
    'for': {
      'en': 'for',
      'hi': 'के लिए',
      'te': 'కోసం',
      'ta': 'க்காக',
    },
    'cancel': {
      'en': 'Cancel',
      'hi': 'रद्द करें',
      'te': 'రద్దు చేయండి',
      'ta': 'ரத்து செய்',
    },
    'confirm': {
      'en': 'Confirm',
      'hi': 'पुष्टि करें',
      'te': 'నిర్ధారించండి',
      'ta': 'உறுதிப்படுத்து',
    },
    'booked_successfully': {
      'en': 'booked successfully!',
      'hi': 'सफलतापूर्वक बुक किया गया!',
      'te': 'విజయవంతంగా బుక్ చేయబడింది!',
      'ta': 'வெற்றிகரமாக முன்பதிவு செய்யப்பட்டது!',
    },
    'medical_emergency': {
      'en': 'Medical Emergency',
      'hi': 'चिकित्सा आपातकाल',
      'te': 'వైద్య అత్యవసరం',
      'ta': 'மருத்துவ அவசரநிலை',
    },
    'police': {
      'en': 'Police',
      'hi': 'पुलिस',
      'te': 'పోలీసు',
      'ta': 'காவல்துறை',
    },
    'fire_emergency': {
      'en': 'Fire Emergency',
      'hi': 'अग्नि आपातकाल',
      'te': 'అగ్ని అత్యవసరం',
      'ta': 'தீ அவசரநிலை',
    },

    // Live Queue Screen
    'live_queue_status': {
      'en': 'Live Queue Status',
      'hi': 'लाइव कतार स्थिति',
      'te': 'లైవ్ క్యూ స్థితి',
      'ta': 'நேரடி வரிசை நிலை',
    },
    'main_ghat': {
      'en': 'Main Ghat',
      'hi': 'मुख्य घाट',
      'te': 'ప్రధాన ఘాట్',
      'ta': 'முக்கிய காட்',
    },
    'temple_darshan': {
      'en': 'Temple Darshan',
      'hi': 'मंदिर दर्शन',
      'te': 'దేవాలయ దర్శనం',
      'ta': 'கோயில் தரிசனம்',
    },
    'prasadam_counter': {
      'en': 'Prasadam Counter',
      'hi': 'प्रसादम काउंटर',
      'te': 'ప్రసాదం కౌంటర్',
      'ta': 'பிரசாதம் கவுண்டர்',
    },
    'parking_area': {
      'en': 'Parking Area',
      'hi': 'पार्किंग क्षेत्र',
      'te': 'పార్కింగ్ ఏరియా',
      'ta': 'பார்க்கிங் பகுதி',
    },
    'real_time_updates': {
      'en': 'Real-time Updates',
      'hi': 'रियल-टाइम अपडेट',
      'te': 'రియల్ టైమ్ అప్‌డేట్‌లు',
      'ta': 'நிகழ்நேர புதுப்பிப்புகள்',
    },
    'updated_every_30_sec': {
      'en': 'Updated every 30 seconds',
      'hi': 'हर 30 सेकंड में अपडेट',
      'te': 'ప్రతి 30 సెకన్లకు అప్‌డేట్',
      'ta': 'ஒவ்வொரு 30 வினாடிகளுக்கும் புதுப்பிக்கப்படும்',
    },
    'queue_status_legend': {
      'en': 'Queue Status Legend',
      'hi': 'कतार स्थिति लेजेंड',
      'te': 'క్యూ స్థితి లెజెండ్',
      'ta': 'வரிசை நிலை விளக்கம்',
    },
    'low_wait': {
      'en': 'Low Wait',
      'hi': 'कम प्रतीक्षा',
      'te': 'తక్కువ వేచి ఉండటం',
      'ta': 'குறைந்த காத்திருப்பு',
    },
    'moderate_wait': {
      'en': 'Moderate Wait',
      'hi': 'मध्यम प्रतीक्षा',
      'te': 'మధ్యస్థ వేచి ఉండటం',
      'ta': 'மிதமான காத்திருப்பு',
    },
    'high_wait': {
      'en': 'High Wait',
      'hi': 'अधिक प्रतीक्षा',
      'te': 'ఎక్కువ వేచి ఉండటం',
      'ta': 'அதிக காத்திருப்பு',
    },
    'people_in_queue': {
      'en': 'People in Queue',
      'hi': 'कतार में लोग',
      'te': 'క్యూలో వ్యక్తులు',
      'ta': 'வரிசையில் உள்ளவர்கள்',
    },
    'estimated_wait': {
      'en': 'Estimated Wait',
      'hi': 'अनुमानित प्रतीक्षा',
      'te': 'అంచనా వేచి ఉండటం',
      'ta': 'மதிப்பிடப்பட்ட காத்திருப்பு',
    },
    'minutes': {
      'en': 'min',
      'hi': 'मिनट',
      'te': 'నిమిషాలు',
      'ta': 'நிமிடங்கள்',
    },
    'last_updated': {
      'en': 'Last updated',
      'hi': 'अंतिम अपडेट',
      'te': 'చివరిగా అప్‌డేట్ చేయబడింది',
      'ta': 'கடைசியாக புதுப்பிக்கப்பட்டது',
    },
  };
  
  static String translate(String key, String languageCode) {
    final translations = _translations[key];
    if (translations == null) return key;
    
    return translations[languageCode] ?? translations['en'] ?? key;
  }
  
  static String t(String key, String languageCode) {
    return translate(key, languageCode);
  }
}
