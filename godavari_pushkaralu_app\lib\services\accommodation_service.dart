import 'dart:async';
import 'dart:math';
import 'package:geolocator/geolocator.dart';

enum AccommodationType { dharamshala, guestHouse, hotel, homestay }
enum AvailabilityStatus { available, limited, full, waitlist }
enum FoodType { vegetarian, nonVegetarian, jain, vegan }

class Accommodation {
  final String id;
  final String name;
  final AccommodationType type;
  final String location;
  final double latitude;
  final double longitude;
  final double basePrice;
  final double currentPrice;
  final int totalRooms;
  final int availableRooms;
  final AvailabilityStatus status;
  final double rating;
  final int reviewCount;
  final List<String> amenities;
  final List<String> images;
  final String description;
  final String contactNumber;
  final bool hasFood;
  final List<FoodType> foodTypes;
  final DateTime lastUpdated;
  final bool isVerified;
  final double distanceFromGhat;

  Accommodation({
    required this.id,
    required this.name,
    required this.type,
    required this.location,
    required this.latitude,
    required this.longitude,
    required this.basePrice,
    required this.currentPrice,
    required this.totalRooms,
    required this.availableRooms,
    required this.status,
    required this.rating,
    required this.reviewCount,
    required this.amenities,
    required this.images,
    required this.description,
    required this.contactNumber,
    required this.hasFood,
    required this.foodTypes,
    required this.lastUpdated,
    required this.isVerified,
    required this.distanceFromGhat,
  });

  Accommodation copyWith({
    int? availableRooms,
    AvailabilityStatus? status,
    double? currentPrice,
    DateTime? lastUpdated,
  }) {
    return Accommodation(
      id: id,
      name: name,
      type: type,
      location: location,
      latitude: latitude,
      longitude: longitude,
      basePrice: basePrice,
      currentPrice: currentPrice ?? this.currentPrice,
      totalRooms: totalRooms,
      availableRooms: availableRooms ?? this.availableRooms,
      status: status ?? this.status,
      rating: rating,
      reviewCount: reviewCount,
      amenities: amenities,
      images: images,
      description: description,
      contactNumber: contactNumber,
      hasFood: hasFood,
      foodTypes: foodTypes,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isVerified: isVerified,
      distanceFromGhat: distanceFromGhat,
    );
  }
}

class FoodService {
  final String id;
  final String name;
  final String location;
  final double latitude;
  final double longitude;
  final List<FoodType> foodTypes;
  final double rating;
  final int reviewCount;
  final String priceRange;
  final bool isAnnadanam;
  final String timings;
  final String contactNumber;
  final List<String> specialities;
  final bool isOpen;
  final DateTime lastUpdated;

  FoodService({
    required this.id,
    required this.name,
    required this.location,
    required this.latitude,
    required this.longitude,
    required this.foodTypes,
    required this.rating,
    required this.reviewCount,
    required this.priceRange,
    required this.isAnnadanam,
    required this.timings,
    required this.contactNumber,
    required this.specialities,
    required this.isOpen,
    required this.lastUpdated,
  });
}

class BookingRequest {
  final String accommodationId;
  final DateTime checkIn;
  final DateTime checkOut;
  final int guests;
  final String guestName;
  final String guestPhone;
  final String guestEmail;

  BookingRequest({
    required this.accommodationId,
    required this.checkIn,
    required this.checkOut,
    required this.guests,
    required this.guestName,
    required this.guestPhone,
    required this.guestEmail,
  });
}

class AccommodationService {
  static AccommodationService? _instance;

  factory AccommodationService() {
    return _instance ??= AccommodationService._internal();
  }

  AccommodationService._internal();

  // For testing - create a new instance
  AccommodationService.newInstance();

  final StreamController<List<Accommodation>> _accommodationsController = 
      StreamController<List<Accommodation>>.broadcast();
  final StreamController<List<FoodService>> _foodServicesController = 
      StreamController<List<FoodService>>.broadcast();

  Stream<List<Accommodation>> get accommodationsStream => _accommodationsController.stream;
  Stream<List<FoodService>> get foodServicesStream => _foodServicesController.stream;

  Timer? _updateTimer;
  List<Accommodation> _accommodations = [];
  List<FoodService> _foodServices = [];
  Position? _currentPosition;

  void initialize() {
    _initializeAccommodations();
    _initializeFoodServices();
    _startRealTimeUpdates();
    _getCurrentLocation();
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) return;

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) return;
      }

      _currentPosition = await Geolocator.getCurrentPosition();
      _updateDistances();
    } catch (e) {
      // Handle location error
    }
  }

  void _initializeAccommodations() {
    _accommodations = [
      // Dharamshalas
      Accommodation(
        id: 'dh_001',
        name: 'Sri Rama Dharmashala',
        type: AccommodationType.dharamshala,
        location: 'Pushkar Ghat Area',
        latitude: 17.0081,
        longitude: 81.7734,
        basePrice: 300,
        currentPrice: 350,
        totalRooms: 50,
        availableRooms: 12,
        status: AvailabilityStatus.limited,
        rating: 4.2,
        reviewCount: 156,
        amenities: ['Free WiFi', 'Shared Bathroom', 'Prayer Hall', 'Free Breakfast'],
        images: ['dharamshala1.jpg', 'dharamshala2.jpg'],
        description: 'Traditional dharamshala near main ghat with spiritual atmosphere',
        contactNumber: '+91-883-2420601',
        hasFood: true,
        foodTypes: [FoodType.vegetarian],
        lastUpdated: DateTime.now(),
        isVerified: true,
        distanceFromGhat: 0.2,
      ),
      Accommodation(
        id: 'dh_002',
        name: 'Godavari Seva Dharmashala',
        type: AccommodationType.dharamshala,
        location: 'Ghat Road',
        latitude: 17.0085,
        longitude: 81.7740,
        basePrice: 250,
        currentPrice: 300,
        totalRooms: 40,
        availableRooms: 8,
        status: AvailabilityStatus.limited,
        rating: 4.0,
        reviewCount: 89,
        amenities: ['Shared Bathroom', 'Prayer Hall', 'Free Meals', 'Parking'],
        images: ['dharamshala3.jpg'],
        description: 'Simple accommodation with basic facilities near river',
        contactNumber: '+91-883-2420602',
        hasFood: true,
        foodTypes: [FoodType.vegetarian, FoodType.jain],
        lastUpdated: DateTime.now(),
        isVerified: true,
        distanceFromGhat: 0.1,
      ),

      // Guest Houses
      Accommodation(
        id: 'gh_001',
        name: 'Pushkaralu Guest House',
        type: AccommodationType.guestHouse,
        location: 'Main Road',
        latitude: 17.0075,
        longitude: 81.7720,
        basePrice: 800,
        currentPrice: 950,
        totalRooms: 25,
        availableRooms: 5,
        status: AvailabilityStatus.limited,
        rating: 4.3,
        reviewCount: 234,
        amenities: ['AC Rooms', 'Private Bathroom', 'WiFi', 'Restaurant', 'Parking'],
        images: ['guesthouse1.jpg', 'guesthouse2.jpg'],
        description: 'Comfortable guest house with modern amenities',
        contactNumber: '+91-883-2420701',
        hasFood: true,
        foodTypes: [FoodType.vegetarian, FoodType.nonVegetarian],
        lastUpdated: DateTime.now(),
        isVerified: true,
        distanceFromGhat: 0.5,
      ),

      // Hotels
      Accommodation(
        id: 'ht_001',
        name: 'Hotel Godavari Palace',
        type: AccommodationType.hotel,
        location: 'City Center',
        latitude: 17.0060,
        longitude: 81.7700,
        basePrice: 2500,
        currentPrice: 2800,
        totalRooms: 60,
        availableRooms: 15,
        status: AvailabilityStatus.available,
        rating: 4.5,
        reviewCount: 445,
        amenities: ['AC Rooms', 'Restaurant', 'Room Service', 'WiFi', 'Spa', 'Pool'],
        images: ['hotel1.jpg', 'hotel2.jpg', 'hotel3.jpg'],
        description: 'Luxury hotel with premium facilities and services',
        contactNumber: '+91-883-2420801',
        hasFood: true,
        foodTypes: [FoodType.vegetarian, FoodType.nonVegetarian, FoodType.jain],
        lastUpdated: DateTime.now(),
        isVerified: true,
        distanceFromGhat: 1.2,
      ),

      // Homestays
      Accommodation(
        id: 'hs_001',
        name: 'Rajahmundry Family Homestay',
        type: AccommodationType.homestay,
        location: 'Residential Area',
        latitude: 17.0090,
        longitude: 81.7750,
        basePrice: 1200,
        currentPrice: 1400,
        totalRooms: 3,
        availableRooms: 1,
        status: AvailabilityStatus.limited,
        rating: 4.7,
        reviewCount: 67,
        amenities: ['Home Cooked Food', 'Family Environment', 'WiFi', 'Local Guide'],
        images: ['homestay1.jpg'],
        description: 'Experience local culture with warm hospitality',
        contactNumber: '+91-883-2420901',
        hasFood: true,
        foodTypes: [FoodType.vegetarian],
        lastUpdated: DateTime.now(),
        isVerified: true,
        distanceFromGhat: 0.8,
      ),
    ];

    _accommodationsController.add(_accommodations);
  }

  void _initializeFoodServices() {
    _foodServices = [
      FoodService(
        id: 'fs_001',
        name: 'Annadanam Seva Center',
        location: 'Main Ghat',
        latitude: 17.0081,
        longitude: 81.7734,
        foodTypes: [FoodType.vegetarian, FoodType.jain],
        rating: 4.8,
        reviewCount: 1200,
        priceRange: 'Free',
        isAnnadanam: true,
        timings: '6:00 AM - 10:00 PM',
        contactNumber: '+91-883-2421001',
        specialities: ['Prasadam', 'Traditional Meals', 'Sweets'],
        isOpen: true,
        lastUpdated: DateTime.now(),
      ),
      FoodService(
        id: 'fs_002',
        name: 'Godavari Restaurant',
        location: 'Near Guest House',
        latitude: 17.0075,
        longitude: 81.7720,
        foodTypes: [FoodType.vegetarian, FoodType.nonVegetarian],
        rating: 4.2,
        reviewCount: 345,
        priceRange: '₹150-400',
        isAnnadanam: false,
        timings: '7:00 AM - 11:00 PM',
        contactNumber: '+91-883-2421002',
        specialities: ['South Indian', 'North Indian', 'Chinese'],
        isOpen: true,
        lastUpdated: DateTime.now(),
      ),
      FoodService(
        id: 'fs_003',
        name: 'Pure Veg Dhaba',
        location: 'Highway Road',
        latitude: 17.0065,
        longitude: 81.7710,
        foodTypes: [FoodType.vegetarian, FoodType.jain],
        rating: 4.0,
        reviewCount: 189,
        priceRange: '₹80-250',
        isAnnadanam: false,
        timings: '6:00 AM - 12:00 AM',
        contactNumber: '+91-883-2421003',
        specialities: ['Punjabi Food', 'Thali', 'Fresh Rotis'],
        isOpen: true,
        lastUpdated: DateTime.now(),
      ),
    ];

    _foodServicesController.add(_foodServices);
  }

  void _startRealTimeUpdates() {
    _updateTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _updateAvailability();
    });
  }

  void _updateAvailability() {
    final random = Random();

    _accommodations = _accommodations.map((accommodation) {
      // Simulate booking activity
      int availableRooms = accommodation.availableRooms;
      double currentPrice = accommodation.currentPrice;

      // Random booking/cancellation
      if (random.nextDouble() < 0.3) {
        if (random.nextBool() && availableRooms > 0) {
          availableRooms = (availableRooms - random.nextInt(3)).clamp(0, accommodation.totalRooms);
        } else if (availableRooms < accommodation.totalRooms) {
          availableRooms = (availableRooms + random.nextInt(2)).clamp(0, accommodation.totalRooms);
        }
      }

      // Dynamic pricing based on availability
      double occupancyRate = (accommodation.totalRooms - availableRooms) / accommodation.totalRooms;
      if (occupancyRate > 0.8) {
        currentPrice = accommodation.basePrice * 1.25; // 25% surge
      } else if (occupancyRate > 0.6) {
        currentPrice = accommodation.basePrice * 1.15; // 15% surge
      } else {
        currentPrice = accommodation.basePrice;
      }

      // Update status based on availability
      AvailabilityStatus status;
      if (availableRooms == 0) {
        status = AvailabilityStatus.full;
      } else if (availableRooms <= accommodation.totalRooms * 0.2) {
        status = AvailabilityStatus.limited;
      } else {
        status = AvailabilityStatus.available;
      }

      return accommodation.copyWith(
        availableRooms: availableRooms,
        status: status,
        currentPrice: currentPrice,
        lastUpdated: DateTime.now(),
      );
    }).toList();

    _accommodationsController.add(_accommodations);
  }

  void _updateDistances() {
    if (_currentPosition == null) return;

    _accommodations = _accommodations.map((accommodation) {
      // Distance calculation is handled in the accommodation model
      return accommodation.copyWith();
    }).toList();

    _accommodationsController.add(_accommodations);
  }

  // Public methods
  List<Accommodation> get accommodations => List.from(_accommodations);
  List<FoodService> get foodServices => List.from(_foodServices);

  List<Accommodation> getAccommodationsByType(AccommodationType type) {
    return _accommodations.where((acc) => acc.type == type).toList();
  }

  List<Accommodation> getAvailableAccommodations() {
    return _accommodations.where((acc) => acc.status != AvailabilityStatus.full).toList();
  }

  List<Accommodation> getNearbyAccommodations(double maxDistance) {
    return _accommodations.where((acc) => acc.distanceFromGhat <= maxDistance).toList();
  }

  List<FoodService> getAnnadanamServices() {
    return _foodServices.where((fs) => fs.isAnnadanam).toList();
  }

  Future<String> bookAccommodation(BookingRequest request) async {
    // Simulate booking process
    await Future.delayed(const Duration(seconds: 2));

    final accommodation = _accommodations.firstWhere((acc) => acc.id == request.accommodationId);
    if (accommodation.availableRooms > 0) {
      // Update availability
      final updatedAccommodation = accommodation.copyWith(
        availableRooms: accommodation.availableRooms - 1,
        lastUpdated: DateTime.now(),
      );

      final index = _accommodations.indexWhere((acc) => acc.id == request.accommodationId);
      _accommodations[index] = updatedAccommodation;
      _accommodationsController.add(_accommodations);

      return 'BK${DateTime.now().millisecondsSinceEpoch}';
    } else {
      throw Exception('No rooms available');
    }
  }

  void dispose() {
    _updateTimer?.cancel();
    _accommodationsController.close();
    _foodServicesController.close();
  }
}
