import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../services/localization_service.dart';
import '../services/language_service.dart';
import 'dart:async';
import 'dart:math';

class LiveQueueScreen extends StatefulWidget {
  const LiveQueueScreen({super.key});

  @override
  State<LiveQueueScreen> createState() => _LiveQueueScreenState();
}

class _LiveQueueScreenState extends State<LiveQueueScreen> {
  String get _selectedLanguage => languageService.currentLanguageCode;
  Timer? _updateTimer;
  List<QueueInfo> _queues = [];

  @override
  void initState() {
    super.initState();
    _initializeQueues();
    _startAutoUpdate();
  }

  @override
  void dispose() {
    _updateTimer?.cancel();
    super.dispose();
  }

  void _initializeQueues() {
    _queues = [
      QueueInfo(
        id: 'main_ghat',
        name: LocalizationService.t('main_ghat', _selectedLanguage),
        currentCount: 150,
        estimatedWait: 45,
        status: QueueStatus.moderate,
        lastUpdated: DateTime.now(),
      ),
      QueueInfo(
        id: 'temple_darshan',
        name: LocalizationService.t('temple_darshan', _selectedLanguage),
        currentCount: 89,
        estimatedWait: 25,
        status: QueueStatus.low,
        lastUpdated: DateTime.now(),
      ),
      QueueInfo(
        id: 'vip_darshan',
        name: LocalizationService.t('vip_darshan', _selectedLanguage),
        currentCount: 12,
        estimatedWait: 5,
        status: QueueStatus.low,
        lastUpdated: DateTime.now(),
      ),
      QueueInfo(
        id: 'prasadam_counter',
        name: LocalizationService.t('prasadam_counter', _selectedLanguage),
        currentCount: 234,
        estimatedWait: 60,
        status: QueueStatus.high,
        lastUpdated: DateTime.now(),
      ),
      QueueInfo(
        id: 'parking_area',
        name: LocalizationService.t('parking_area', _selectedLanguage),
        currentCount: 78,
        estimatedWait: 15,
        status: QueueStatus.low,
        lastUpdated: DateTime.now(),
      ),
    ];
  }

  void _startAutoUpdate() {
    _updateTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _updateQueueData();
    });
  }

  void _updateQueueData() {
    setState(() {
      for (var queue in _queues) {
        // Simulate real-time updates
        final random = Random();
        final change = random.nextInt(21) - 10; // -10 to +10
        queue.currentCount = (queue.currentCount + change).clamp(0, 500);
        queue.estimatedWait = (queue.currentCount * 0.3).round();
        queue.lastUpdated = DateTime.now();
        
        // Update status based on count
        if (queue.currentCount < 50) {
          queue.status = QueueStatus.low;
        } else if (queue.currentCount < 150) {
          queue.status = QueueStatus.moderate;
        } else {
          queue.status = QueueStatus.high;
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0D47A1),
      appBar: AppBar(
        title: Text(
          LocalizationService.t('live_queue_status', _selectedLanguage),
          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF0D47A1),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _updateQueueData,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF0D47A1), Color(0xFF42A5F5), Color(0xFFFFD700)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header Info
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.access_time, color: Colors.white, size: 24),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            LocalizationService.t('real_time_updates', _selectedLanguage),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            LocalizationService.t('updated_every_30_sec', _selectedLanguage),
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.8),
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.circle, color: Colors.white, size: 8),
                          const SizedBox(width: 6),
                          Text(
                            LocalizationService.t('live', _selectedLanguage),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Queue List
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _queues.length,
                  itemBuilder: (context, index) {
                    final queue = _queues[index];
                    return _buildQueueCard(queue);
                  },
                ),
              ),

              // Legend
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      LocalizationService.t('queue_status_legend', _selectedLanguage),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildLegendItem(Colors.green, LocalizationService.t('low_wait', _selectedLanguage)),
                        _buildLegendItem(Colors.orange, LocalizationService.t('moderate_wait', _selectedLanguage)),
                        _buildLegendItem(Colors.red, LocalizationService.t('high_wait', _selectedLanguage)),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQueueCard(QueueInfo queue) {
    Color statusColor;
    IconData statusIcon;
    
    switch (queue.status) {
      case QueueStatus.low:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case QueueStatus.moderate:
        statusColor = Colors.orange;
        statusIcon = Icons.warning;
        break;
      case QueueStatus.high:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(statusIcon, color: statusColor, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    queue.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${queue.currentCount}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    Icons.people,
                    LocalizationService.t('people_in_queue', _selectedLanguage),
                    '${queue.currentCount}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    Icons.schedule,
                    LocalizationService.t('estimated_wait', _selectedLanguage),
                    '${queue.estimatedWait} ${LocalizationService.t('minutes', _selectedLanguage)}',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              '${LocalizationService.t('last_updated', _selectedLanguage)}: ${_formatTime(queue.lastUpdated)}',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, color: Colors.white, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 12,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem(Color color, String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}

enum QueueStatus { low, moderate, high }

class QueueInfo {
  final String id;
  final String name;
  int currentCount;
  int estimatedWait;
  QueueStatus status;
  DateTime lastUpdated;

  QueueInfo({
    required this.id,
    required this.name,
    required this.currentCount,
    required this.estimatedWait,
    required this.status,
    required this.lastUpdated,
  });
}
