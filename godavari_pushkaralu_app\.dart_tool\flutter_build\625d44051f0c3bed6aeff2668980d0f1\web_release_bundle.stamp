{"inputs": ["D:\\godavari pushkaralu\\godavari_pushkaralu_app\\pubspec.yaml", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\.dart_tool\\flutter_build\\625d44051f0c3bed6aeff2668980d0f1\\main.dart.js", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\pubspec.yaml", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\images\\devotional_pattern.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\images\\ganga_aarti.jpg", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\images\\godavari_river.jpg", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\images\\placeholder_godavari_temple.jpg", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\images\\pushkar_ghat.jpg", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\images\\temple_background.jpg", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\A generic restroom toilet icon.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\A spiritual Indian style.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\App Logo.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\Diya lamp icon.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\Drinking water icon.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\Food annadanam icon.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\Godavari river, temple, or festival background. Use a beautiful, spiritual photo or illustration.webp", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\lotus flower icon.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\Medical first aid icon.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\placeholder_app_logo.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\placeholder_bell.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\placeholder_bronze_badge.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\placeholder_diya.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\placeholder_food.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\placeholder_gold_badge.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\placeholder_lotus.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\placeholder_medical.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\placeholder_restroom.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\placeholder_silver_badge.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\placeholder_transport.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\placeholder_water.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\royal badge gold.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\temple bell.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\Transport bus train icon.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\vip bronze.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\icons\\vvip silver.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\audio\\daily_puja_mantra.mp3", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\lottie\\Campers Welcome.json", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\lottie\\Confetti.json", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\lottie\\Loading animation.json", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\lottie\\loading.json", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\lottie\\onboard_anim.json", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\assets\\lottie\\success.json", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "C:\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "C:\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\_fe_analyzer_shared-85.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\analyzer-7.7.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\archive-3.6.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\args-2.7.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers-5.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-4.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_darwin-5.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_linux-3.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_platform_interface-6.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_web-4.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_windows-3.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_config-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_daemon-4.0.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_resolvers-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_runner-2.6.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_runner_core-9.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\built_collection-5.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\built_value-8.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image-3.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image_platform_interface-4.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image_web-1.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\checked_yaml-2.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\code_builder-4.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\connectivity_plus-5.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\connectivity_plus_platform_interface-1.2.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dart_style-3.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dbus-0.7.11\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dio-5.9.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dio_web_adapter-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-2.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.29\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_svg-2.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\frontend_server_client-4.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator-10.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_android-4.6.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_apple-2.3.13\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_platform_interface-4.2.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_web-2.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_windows-0.2.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\glob-2.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-13.2.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps-8.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter-2.12.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_android-2.18.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_ios-2.15.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_platform_interface-2.13.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_web-0.5.12+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\graphs-2.3.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_multi_server-3.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.19.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\io-1.0.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\js-0.6.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\location-5.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\location_platform_interface-3.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\location_web-4.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lottie-2.7.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mockito-5.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\nested-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\nm-0.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\octo_image-2.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_config-2.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus-4.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus_platform_interface-2.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_parsing-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler-11.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_android-12.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_apple-9.4.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_windows-0.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pool-1.5.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pub_semver-2.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pubspec_parse-1.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sanitize_html-2.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.11\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shelf-1.4.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shelf_web_socket-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shimmer-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_gen-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_android-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_darwin-2.4.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_transform-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timing-1.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_web-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics-1.1.19\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics_codec-1.1.13\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics_compiler-1.1.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-14.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\watcher-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket-1.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket_channel-3.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.13.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\yaml-3.1.3\\LICENSE", "C:\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "C:\\flutter\\packages\\flutter\\LICENSE", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD701020247", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\web\\favicon.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\web\\icons\\Icon-192.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\web\\icons\\Icon-512.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\web\\icons\\Icon-maskable-192.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\web\\icons\\Icon-maskable-512.png", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\web\\index.html", "D:\\godavari pushkaralu\\godavari_pushkaralu_app\\web\\manifest.json"], "outputs": ["D:\\godavari pushkaralu\\godavari_pushkaralu_app\\build\\web\\main.dart.js", "build\\web\\assets\\assets/images/devotional_pattern.png", "build\\web\\assets\\assets/images/ganga_aarti.jpg", "build\\web\\assets\\assets/images/godavari_river.jpg", "build\\web\\assets\\assets/images/placeholder_godavari_temple.jpg", "build\\web\\assets\\assets/images/pushkar_ghat.jpg", "build\\web\\assets\\assets/images/temple_background.jpg", "build\\web\\assets\\assets/icons/A%20generic%20restroom%20toilet%20icon.png", "build\\web\\assets\\assets/icons/A%20spiritual%20Indian%20style.png", "build\\web\\assets\\assets/icons/App%20Logo.png", "build\\web\\assets\\assets/icons/Diya%20lamp%20icon.png", "build\\web\\assets\\assets/icons/Drinking%20water%20icon.png", "build\\web\\assets\\assets/icons/Food%20annadanam%20icon.png", "build\\web\\assets\\assets/icons/God<PERSON>ri%20river,%20temple,%20or%20festival%20background.%20Use%20a%20beautiful,%20spiritual%20photo%20or%20illustration.webp", "build\\web\\assets\\assets/icons/lotus%20flower%20icon.png", "build\\web\\assets\\assets/icons/Medical%20first%20aid%20icon.png", "build\\web\\assets\\assets/icons/placeholder_app_logo.png", "build\\web\\assets\\assets/icons/placeholder_bell.png", "build\\web\\assets\\assets/icons/placeholder_bronze_badge.png", "build\\web\\assets\\assets/icons/placeholder_diya.png", "build\\web\\assets\\assets/icons/placeholder_food.png", "build\\web\\assets\\assets/icons/placeholder_gold_badge.png", "build\\web\\assets\\assets/icons/placeholder_lotus.png", "build\\web\\assets\\assets/icons/placeholder_medical.png", "build\\web\\assets\\assets/icons/placeholder_restroom.png", "build\\web\\assets\\assets/icons/placeholder_silver_badge.png", "build\\web\\assets\\assets/icons/placeholder_transport.png", "build\\web\\assets\\assets/icons/placeholder_water.png", "build\\web\\assets\\assets/icons/royal%20badge%20gold.png", "build\\web\\assets\\assets/icons/temple%20bell.png", "build\\web\\assets\\assets/icons/Transport%20bus%20train%20icon.png", "build\\web\\assets\\assets/icons/vip%20bronze.png", "build\\web\\assets\\assets/icons/vvip%20silver.png", "build\\web\\assets\\assets/audio/daily_puja_mantra.mp3", "build\\web\\assets\\assets/lottie/Campers%20Welcome.json", "build\\web\\assets\\assets/lottie/Confetti.json", "build\\web\\assets\\assets/lottie/Loading%20animation.json", "build\\web\\assets\\assets/lottie/loading.json", "build\\web\\assets\\assets/lottie/onboard_anim.json", "build\\web\\assets\\assets/lottie/success.json", "build\\web\\assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "build\\web\\assets\\fonts/MaterialIcons-Regular.otf", "build\\web\\assets\\shaders/ink_sparkle.frag", "build\\web\\assets\\AssetManifest.json", "build\\web\\assets\\AssetManifest.bin", "build\\web\\assets\\AssetManifest.bin.json", "build\\web\\assets\\FontManifest.json", "build\\web\\assets\\NOTICES", "build\\web\\favicon.png", "build\\web\\icons\\Icon-192.png", "build\\web\\icons\\Icon-512.png", "build\\web\\icons\\Icon-maskable-192.png", "build\\web\\icons\\Icon-maskable-512.png", "build\\web\\manifest.json"]}