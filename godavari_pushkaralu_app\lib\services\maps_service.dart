import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class MapsService {
  static final MapsService _instance = MapsService._internal();
  factory MapsService() => _instance;
  MapsService._internal();

  // Real-time data streams
  final StreamController<Map<String, GhatData>> _ghatDataController = 
      StreamController<Map<String, GhatData>>.broadcast();
  final StreamController<Position?> _locationController = 
      StreamController<Position?>.broadcast();

  Stream<Map<String, GhatData>> get ghatDataStream => _ghatDataController.stream;
  Stream<Position?> get locationStream => _locationController.stream;

  Timer? _dataUpdateTimer;
  Position? _currentPosition;

  // Ghat locations with real coordinates
  final Map<String, GhatLocation> _ghatLocations = {
    'rajahmundry': GhatLocation(
      id: 'rajahmundry',
      name: 'Rajahmundry Ghat',
      location: 'Main Headquarters, Rajahmundry',
      coordinates: const LatLng(17.0005, 81.7880),
      facilities: ['parking', 'restroom', 'medical', 'food'],
    ),
    'bhadrachalam': GhatLocation(
      id: 'bhadrachalam',
      name: 'Bhadrachalam Ghat',
      location: 'Temple Town, Bhadrachalam',
      coordinates: const LatLng(17.6688, 80.8936),
      facilities: ['parking', 'restroom', 'food', 'temple_access'],
    ),
    'basara': GhatLocation(
      id: 'basara',
      name: 'Basara Ghat',
      location: 'Saraswati Temple, Basara',
      coordinates: const LatLng(18.1200, 77.1400),
      facilities: ['parking', 'restroom', 'medical', 'educational_tours'],
    ),
  };

  // Initialize the service
  Future<void> initialize() async {
    await _requestLocationPermission();
    await _getCurrentLocation();
    _startRealTimeUpdates();
  }

  // Request location permissions
  Future<bool> _requestLocationPermission() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return false;
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return false;
    }

    return true;
  }

  // Get current location
  Future<void> _getCurrentLocation() async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      _currentPosition = position;
      _locationController.add(position);

      // Listen for location changes
      Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10,
        ),
      ).listen((Position position) {
        _currentPosition = position;
        _locationController.add(position);
      });
    } catch (e) {
      print('Error getting location: $e');
    }
  }

  // Start real-time data updates
  void _startRealTimeUpdates() {
    _dataUpdateTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _updateGhatData();
    });
    
    // Initial update
    _updateGhatData();
  }

  // Simulate real-time ghat data updates
  void _updateGhatData() {
    final random = Random();
    final now = DateTime.now();
    
    Map<String, GhatData> updatedData = {};
    
    for (String ghatId in _ghatLocations.keys) {
      final ghat = _ghatLocations[ghatId]!;
      
      // Simulate crowd levels based on time and random factors
      CrowdLevel crowdLevel = _calculateCrowdLevel(now, random);
      
      // Simulate facility statuses
      Map<String, FacilityStatus> facilityStatuses = {};
      for (String facility in ghat.facilities) {
        facilityStatuses[facility] = _generateFacilityStatus(facility, random);
      }
      
      // Simulate current events
      List<LiveEvent> currentEvents = _generateCurrentEvents(ghatId, now);
      
      // Get weather data (mock for now)
      WeatherData weather = _generateWeatherData(random);
      
      // Calculate distance from user
      double? distanceKm;
      if (_currentPosition != null) {
        distanceKm = Geolocator.distanceBetween(
          _currentPosition!.latitude,
          _currentPosition!.longitude,
          ghat.coordinates.latitude,
          ghat.coordinates.longitude,
        ) / 1000; // Convert to km
      }
      
      updatedData[ghatId] = GhatData(
        ghat: ghat,
        crowdLevel: crowdLevel,
        facilityStatuses: facilityStatuses,
        currentEvents: currentEvents,
        weather: weather,
        distanceKm: distanceKm,
        lastUpdated: now,
      );
    }
    
    _ghatDataController.add(updatedData);
  }

  // Calculate crowd level based on time and other factors
  CrowdLevel _calculateCrowdLevel(DateTime now, Random random) {
    int hour = now.hour;
    
    // Peak hours: 6-9 AM and 6-8 PM
    if ((hour >= 6 && hour <= 9) || (hour >= 18 && hour <= 20)) {
      return random.nextBool() ? CrowdLevel.high : CrowdLevel.medium;
    }
    // Moderate hours: 10 AM - 5 PM
    else if (hour >= 10 && hour <= 17) {
      return random.nextBool() ? CrowdLevel.medium : CrowdLevel.low;
    }
    // Low hours: Night and early morning
    else {
      return random.nextBool() ? CrowdLevel.low : CrowdLevel.medium;
    }
  }

  // Generate facility status
  FacilityStatus _generateFacilityStatus(String facility, Random random) {
    double availability = 0.3 + (random.nextDouble() * 0.7); // 30-100%
    
    String status;
    if (availability > 0.8) {
      status = 'Excellent';
    } else if (availability > 0.6) {
      status = 'Good';
    } else if (availability > 0.4) {
      status = 'Fair';
    } else {
      status = 'Limited';
    }
    
    return FacilityStatus(
      availability: availability,
      status: status,
      lastCleaned: DateTime.now().subtract(Duration(hours: random.nextInt(6))),
    );
  }

  // Generate current events
  List<LiveEvent> _generateCurrentEvents(String ghatId, DateTime now) {
    List<LiveEvent> events = [];
    
    int hour = now.hour;
    
    // Morning Aarti
    if (hour >= 6 && hour <= 7) {
      events.add(LiveEvent(
        title: 'Morning Aarti',
        description: 'Sacred morning prayers in progress',
        startTime: DateTime(now.year, now.month, now.day, 6, 0),
        endTime: DateTime(now.year, now.month, now.day, 7, 0),
        isLive: true,
        type: EventType.ceremony,
      ));
    }
    
    // Evening Aarti
    if (hour >= 18 && hour <= 19) {
      events.add(LiveEvent(
        title: 'Evening Aarti',
        description: 'Sacred evening prayers with diyas',
        startTime: DateTime(now.year, now.month, now.day, 18, 30),
        endTime: DateTime(now.year, now.month, now.day, 19, 30),
        isLive: true,
        type: EventType.ceremony,
      ));
    }
    
    // Prasadam distribution
    if (hour >= 12 && hour <= 14) {
      events.add(LiveEvent(
        title: 'Prasadam Distribution',
        description: 'Free blessed food distribution',
        startTime: DateTime(now.year, now.month, now.day, 12, 0),
        endTime: DateTime(now.year, now.month, now.day, 14, 0),
        isLive: true,
        type: EventType.service,
      ));
    }
    
    return events;
  }

  // Generate weather data
  WeatherData _generateWeatherData(Random random) {
    return WeatherData(
      temperature: 25 + random.nextInt(10), // 25-35°C
      humidity: 60 + random.nextInt(30), // 60-90%
      condition: ['Sunny', 'Partly Cloudy', 'Cloudy'][random.nextInt(3)],
      windSpeed: 5 + random.nextInt(10), // 5-15 km/h
    );
  }

  // Navigation methods
  Future<void> navigateToGhat(String ghatId) async {
    final ghat = _ghatLocations[ghatId];
    if (ghat == null) return;
    
    final url = 'https://www.google.com/maps/dir/?api=1&destination=${ghat.coordinates.latitude},${ghat.coordinates.longitude}&travelmode=driving';
    
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  // Get ghat by ID
  GhatLocation? getGhat(String ghatId) => _ghatLocations[ghatId];
  
  // Get all ghats
  Map<String, GhatLocation> getAllGhats() => Map.from(_ghatLocations);

  // Dispose resources
  void dispose() {
    _dataUpdateTimer?.cancel();
    _ghatDataController.close();
    _locationController.close();
  }
}

// Data models
class GhatLocation {
  final String id;
  final String name;
  final String location;
  final LatLng coordinates;
  final List<String> facilities;

  const GhatLocation({
    required this.id,
    required this.name,
    required this.location,
    required this.coordinates,
    required this.facilities,
  });
}

class GhatData {
  final GhatLocation ghat;
  final CrowdLevel crowdLevel;
  final Map<String, FacilityStatus> facilityStatuses;
  final List<LiveEvent> currentEvents;
  final WeatherData weather;
  final double? distanceKm;
  final DateTime lastUpdated;

  const GhatData({
    required this.ghat,
    required this.crowdLevel,
    required this.facilityStatuses,
    required this.currentEvents,
    required this.weather,
    this.distanceKm,
    required this.lastUpdated,
  });
}

enum CrowdLevel { low, medium, high }

class FacilityStatus {
  final double availability; // 0.0 to 1.0
  final String status;
  final DateTime lastCleaned;

  const FacilityStatus({
    required this.availability,
    required this.status,
    required this.lastCleaned,
  });
}

class LiveEvent {
  final String title;
  final String description;
  final DateTime startTime;
  final DateTime endTime;
  final bool isLive;
  final EventType type;

  const LiveEvent({
    required this.title,
    required this.description,
    required this.startTime,
    required this.endTime,
    required this.isLive,
    required this.type,
  });
}

enum EventType { ceremony, service, announcement }

class WeatherData {
  final int temperature;
  final int humidity;
  final String condition;
  final int windSpeed;

  const WeatherData({
    required this.temperature,
    required this.humidity,
    required this.condition,
    required this.windSpeed,
  });
}
