// Temporarily commented out due to Firebase dependency issues
// import 'package:firebase_core/firebase_core.dart';
// import 'package:flutter/foundation.dart';

// class FirebaseService {
//   static Future<void> initializeFirebase() async {
//     try {
//       await Firebase.initializeApp(
//         options: kIsWeb
//             ? const FirebaseOptions(
//                 apiKey: "your-api-key",
//                 authDomain: "your-auth-domain",
//                 projectId: "your-project-id",
//                 storageBucket: "your-storage-bucket",
//                 messagingSenderId: "your-messaging-sender-id",
//                 appId: "your-app-id",
//                 measurementId: "your-measurement-id",
//               )
//             : null,
//       );
//     } catch (e) {
//       debugPrint('Firebase initialization error: $e');
//     }
//   }
// } 