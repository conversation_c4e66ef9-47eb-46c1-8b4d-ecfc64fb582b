import 'package:flutter/material.dart';

class AccommodationScreen extends StatelessWidget {
  const AccommodationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Accommodation'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: <PERSON>umn(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Filter Options
            SizedBox(
              height: 50,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  _buildFilterChip('All', true),
                  _buildFilterChip('Hotels', false),
                  _buildFilterChip('Dharamshalas', false),
                  _buildFilterChip('Guest Houses', false),
                  _buildFilterChip('Tents', false),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            const Text(
              'Available Accommodations',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            
            const SizedBox(height: 16),
            
            _buildAccommodationCard(
              'Haritha Hotel Kaleshwaram',
              'Government hotel with modern amenities',
              '₹2,500/night',
              4.2,
              Icons.hotel,
              Colors.blue,
              '0.2 km from main ghat',
            ),
            
            _buildAccommodationCard(
              'Sri Venkateswara Lodge',
              'Budget-friendly accommodation',
              '₹800/night',
              3.8,
              Icons.home,
              Colors.green,
              '0.5 km from main ghat',
            ),
            
            _buildAccommodationCard(
              'Pushkaralu Tent City',
              'Temporary tents with basic facilities',
              '₹500/night',
              3.5,
              Icons.camping,
              Colors.orange,
              '1.0 km from main ghat',
            ),
            
            _buildAccommodationCard(
              'Godavari Guest House',
              'Clean rooms with AC and hot water',
              '₹1,200/night',
              4.0,
              Icons.business,
              Colors.purple,
              '0.8 km from main ghat',
            ),
            
            _buildAccommodationCard(
              'Dharamshala Complex',
              'Free accommodation for pilgrims',
              'Free',
              3.2,
              Icons.temple_hindu,
              Colors.red,
              '1.5 km from main ghat',
            ),
            
            _buildAccommodationCard(
              'River View Resort',
              'Premium resort with river view',
              '₹4,000/night',
              4.5,
              Icons.resort,
              Colors.teal,
              '2.0 km from main ghat',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (bool selected) {
          // Handle filter selection
        },
        selectedColor: const Color(0xFF4CAF50).withValues(alpha: 0.2),
        checkmarkColor: const Color(0xFF4CAF50),
      ),
    );
  }

  Widget _buildAccommodationCard(
    String name,
    String description,
    String price,
    double rating,
    IconData icon,
    Color color,
    String distance,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      price,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF4CAF50),
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.star,
                          size: 16,
                          color: Colors.amber,
                        ),
                        Text(
                          rating.toString(),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  distance,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Booking $name...')),
                    );
                  },
                  child: const Text('Book Now'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
