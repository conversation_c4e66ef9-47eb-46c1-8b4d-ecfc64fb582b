import 'package:flutter/material.dart';
import 'package:godavari_pushkaralu_app/constants/theme.dart';

class GhatsScreen extends StatelessWidget {
  const GhatsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sacred Ghats'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildGhatCard(
            context,
            'Rajahmundry Ghat',
            'Main Headquarters',
            'East Godavari District, AP',
            '17.0005° N, 81.7880° E',
            'Main ceremonial center with Godavari bridge view',
            Icons.location_city,
            AppTheme.primaryColor,
          ),
          _buildGhatCard(
            context,
            'Bhadrachalam Ghat',
            'Sacred Temple Town',
            'Bhadradri Kothagudem District, TS',
            '17.6688° N, 80.8936° E',
            'Sacred town of Lord Rama, ancient temple complex',
            Icons.temple_hindu,
            AppTheme.secondaryColor,
          ),
          _buildGhatCard(
            context,
            'Basara Ghat',
            'Knowledge Center',
            'Nirmal District, TS',
            '18.1200° N, 77.1000° E',
            'Famous Saraswati Temple, knowledge center',
            Icons.school,
            Colors.purple,
          ),
          _buildGhatCard(
            context,
            'Mancherial Ghat',
            'Northern Gateway',
            'Mancherial District, TS',
            '18.8700° N, 79.4600° E',
            'Northern gateway, coal region entry point',
            Icons.north,
            Colors.green,
          ),
          _buildGhatCard(
            context,
            'Kovvur Ghat',
            'Family Zone',
            'West Godavari District, AP',
            '17.0167° N, 81.7333° E',
            'Family-friendly ghat with scenic beauty',
            Icons.family_restroom,
            Colors.orange,
          ),
          _buildGhatCard(
            context,
            'Dhavaleswaram Ghat',
            'Heritage Site',
            'East Godavari District, AP',
            '16.9167° N, 81.6333° E',
            'Historic barrage location, engineering marvel',
            Icons.history_edu,
            Colors.teal,
          ),
        ],
      ),
    );
  }

  Widget _buildGhatCard(
    BuildContext context,
    String name,
    String subtitle,
    String location,
    String coordinates,
    String description,
    IconData icon,
    Color color,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          // TODO: Navigate to ghat details
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          name,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          subtitle,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey[600],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              _buildInfoRow(Icons.location_on, location),
              const SizedBox(height: 4),
              _buildInfoRow(Icons.gps_fixed, coordinates),
              const SizedBox(height: 8),
              Text(
                description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () {
                        // TODO: Open in maps
                      },
                      icon: const Icon(Icons.map),
                      label: const Text('Directions'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        // TODO: Book accommodation
                      },
                      icon: const Icon(Icons.hotel),
                      label: const Text('Book Stay'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }
} 