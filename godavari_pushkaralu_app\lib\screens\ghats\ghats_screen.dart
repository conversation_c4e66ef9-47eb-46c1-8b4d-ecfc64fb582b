import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:godavari_pushkaralu_app/constants/theme.dart';

enum CrowdLevel { low, medium, high }

class GhatInfo {
  final String name;
  final String location;
  final String distance;
  final CrowdLevel crowdLevel;
  final double rating;
  final int reviewCount;
  final List<String> facilities;
  final String specialFeature;
  final bool hasVipAccess;
  final double latitude;
  final double longitude;

  GhatInfo({
    required this.name,
    required this.location,
    required this.distance,
    required this.crowdLevel,
    required this.rating,
    required this.reviewCount,
    required this.facilities,
    required this.specialFeature,
    required this.hasVipAccess,
    required this.latitude,
    required this.longitude,
  });
}

class GhatsScreen extends StatefulWidget {
  const GhatsScreen({super.key});

  @override
  State<GhatsScreen> createState() => _GhatsScreenState();
}

class _GhatsScreenState extends State<GhatsScreen> {
  bool _showNearby = true;
  final TextEditingController _searchController = TextEditingController();

  // Function to open Google Maps with directions
  Future<void> _navigateToGhat(GhatInfo ghat) async {
    // Show loading indicator
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              Text('Opening navigation to ${ghat.name}...'),
            ],
          ),
          backgroundColor: Colors.blue,
          duration: const Duration(seconds: 2),
        ),
      );
    }

    final url = 'https://www.google.com/maps/dir/?api=1&destination=${ghat.latitude},${ghat.longitude}&travelmode=driving';

    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(
          Uri.parse(url),
          mode: LaunchMode.externalApplication,
        );

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Navigation opened successfully!'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        // Fallback: Open Google Maps app directly
        final mapsUrl = 'geo:${ghat.latitude},${ghat.longitude}?q=${ghat.latitude},${ghat.longitude}(${Uri.encodeComponent(ghat.name)})';
        if (await canLaunchUrl(Uri.parse(mapsUrl))) {
          await launchUrl(Uri.parse(mapsUrl));
        } else {
          // Show error message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Could not open maps. Please install Google Maps app.'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening maps: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  final List<GhatInfo> _ghats = [
    // Main Pushkaralu Ghats along Godavari River
    GhatInfo(
      name: 'Rajahmundry Pushkar Ghat',
      location: 'Dowleswaram, Rajahmundry, East Godavari',
      distance: '2.1 km away',
      crowdLevel: CrowdLevel.high,
      rating: 4.8,
      reviewCount: 1250,
      facilities: ['Parking', 'Restroom', 'Medical', 'Food'],
      specialFeature: 'Main Pushkaralu Headquarters',
      hasVipAccess: true,
      latitude: 17.0081,
      longitude: 81.7734,
    ),
    GhatInfo(
      name: 'Bhadrachalam Ghat',
      location: 'Sri Rama Temple, Bhadrachalam',
      distance: '85 km away',
      crowdLevel: CrowdLevel.high,
      rating: 4.9,
      reviewCount: 2100,
      facilities: ['Parking', 'Restroom', 'Food', 'Temple Access'],
      specialFeature: 'Sacred Sri Rama Temple Ghat',
      hasVipAccess: true,
      latitude: 17.6688,
      longitude: 80.8936,
    ),
    GhatInfo(
      name: 'Nashik Ramkund',
      location: 'Ramkund, Nashik, Maharashtra',
      distance: '450 km away',
      crowdLevel: CrowdLevel.high,
      rating: 4.7,
      reviewCount: 3200,
      facilities: ['Parking', 'Restroom', 'Medical', 'Food'],
      specialFeature: 'Most Sacred Pushkaralu Ghat',
      hasVipAccess: true,
      latitude: 19.9975,
      longitude: 73.7898,
    ),
    GhatInfo(
      name: 'Nanded Gurdwara Ghat',
      location: 'Hazur Sahib, Nanded, Maharashtra',
      distance: '320 km away',
      crowdLevel: CrowdLevel.medium,
      rating: 4.6,
      reviewCount: 890,
      facilities: ['Parking', 'Restroom', 'Food', 'Gurdwara'],
      specialFeature: 'Sikh Heritage Ghat',
      hasVipAccess: false,
      latitude: 19.1383,
      longitude: 77.2975,
    ),
    GhatInfo(
      name: 'Basara Saraswati Ghat',
      location: 'Gnana Saraswati Temple, Basara',
      distance: '180 km away',
      crowdLevel: CrowdLevel.medium,
      rating: 4.5,
      reviewCount: 650,
      facilities: ['Parking', 'Restroom', 'Educational Tours'],
      specialFeature: 'Goddess Saraswati Temple',
      hasVipAccess: false,
      latitude: 18.1200,
      longitude: 77.1400,
    ),
    GhatInfo(
      name: 'Dharmapuri Ghat',
      location: 'Dharmapuri, Karimnagar District',
      distance: '220 km away',
      crowdLevel: CrowdLevel.low,
      rating: 4.3,
      reviewCount: 420,
      facilities: ['Parking', 'Restroom', 'Medical'],
      specialFeature: 'Peaceful Riverside Ghat',
      hasVipAccess: false,
      latitude: 18.8833,
      longitude: 78.8500,
    ),
    GhatInfo(
      name: 'Kaleshwaram Ghat',
      location: 'Kaleshwaram Temple, Jayashankar',
      distance: '280 km away',
      crowdLevel: CrowdLevel.medium,
      rating: 4.4,
      reviewCount: 580,
      facilities: ['Parking', 'Restroom', 'Temple Access'],
      specialFeature: 'Ancient Shiva Temple Ghat',
      hasVipAccess: false,
      latitude: 18.8167,
      longitude: 79.9167,
    ),
    GhatInfo(
      name: 'Kovvur Ghat',
      location: 'Kovvur, West Godavari District',
      distance: '25 km away',
      crowdLevel: CrowdLevel.low,
      rating: 4.2,
      reviewCount: 310,
      facilities: ['Parking', 'Restroom'],
      specialFeature: 'Traditional Village Ghat',
      hasVipAccess: false,
      latitude: 17.0167,
      longitude: 81.7333,
    ),
    GhatInfo(
      name: 'Polavaram Ghat',
      location: 'Polavaram, West Godavari District',
      distance: '45 km away',
      crowdLevel: CrowdLevel.low,
      rating: 4.1,
      reviewCount: 280,
      facilities: ['Parking', 'Restroom'],
      specialFeature: 'Scenic River View Ghat',
      hasVipAccess: false,
      latitude: 17.2500,
      longitude: 81.6500,
    ),
    GhatInfo(
      name: 'Mancherial Ghat',
      location: 'Mancherial, Telangana',
      distance: '350 km away',
      crowdLevel: CrowdLevel.low,
      rating: 4.0,
      reviewCount: 190,
      facilities: ['Parking', 'Restroom'],
      specialFeature: 'Serene Bathing Ghat',
      hasVipAccess: false,
      latitude: 18.8667,
      longitude: 79.4667,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: Container(
              color: const Color(0xFFF5F5F5),
              child: Column(
                children: [
                  _buildGhatCounter(),
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _ghats.length,
                      itemBuilder: (context, index) {
                        return _buildGhatCard(_ghats[index]);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomNavBar(),
    );
  }

  Widget _buildHeader() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF1976D2),
            Color(0xFF1565C0),
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Text(
                    'Sacred Ghats',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(
                      Icons.notifications_outlined,
                      color: Colors.white,
                    ),
                  ),
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(
                      Icons.more_vert,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const Text(
                'Godavari Pushkaralu 2027',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 20),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search chats, facilities...',
                    hintStyle: TextStyle(color: Colors.grey[500]),
                    prefixIcon: Icon(Icons.search, color: Colors.grey[500]),
                    suffixIcon: Icon(Icons.tune, color: Colors.grey[500]),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 15,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _showNearby = true;
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: _showNearby ? Colors.white : Colors.transparent,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Show Nearby',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: _showNearby ? const Color(0xFF1976D2) : Colors.white70,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _showNearby = false;
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: !_showNearby ? Colors.white : Colors.transparent,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Show All',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: !_showNearby ? const Color(0xFF1976D2) : Colors.white70,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGhatCounter() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Text(
            '${_ghats.length} Ghats available',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1976D2),
            ),
          ),
          const Spacer(),
          Row(
            children: [
              const Icon(
                Icons.map,
                color: Color(0xFF1976D2),
                size: 20,
              ),
              const SizedBox(width: 4),
              const Text(
                'Map View',
                style: TextStyle(
                  color: Color(0xFF1976D2),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGhatCard(GhatInfo ghat) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        ghat.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(
                            Icons.location_on,
                            size: 16,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              ghat.location,
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getCrowdColor(ghat.crowdLevel),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.people,
                            size: 12,
                            color: _getCrowdTextColor(ghat.crowdLevel),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _getCrowdText(ghat.crowdLevel),
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: _getCrowdTextColor(ghat.crowdLevel),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      ghat.distance,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: ghat.facilities.map((facility) {
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getFacilityIcon(facility),
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        facility,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Row(
                  children: List.generate(5, (index) {
                    return Icon(
                      index < ghat.rating.floor()
                          ? Icons.star
                          : index < ghat.rating
                              ? Icons.star_half
                              : Icons.star_border,
                      size: 16,
                      color: Colors.amber,
                    );
                  }),
                ),
                const SizedBox(width: 8),
                Text(
                  '${ghat.rating} (${ghat.reviewCount} reviews)',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: ghat.hasVipAccess ? Colors.amber[100] : Colors.blue[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    ghat.specialFeature,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: ghat.hasVipAccess ? Colors.amber[800] : Colors.blue[800],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _navigateToGhat(ghat),
                    icon: const Icon(
                      Icons.navigation,
                      size: 18,
                    ),
                    label: const Text('Navigate'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: const Color(0xFF1976D2),
                      side: const BorderSide(color: Color(0xFF1976D2)),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {},
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1976D2),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Details'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getCrowdColor(CrowdLevel level) {
    switch (level) {
      case CrowdLevel.low:
        return Colors.green[100]!;
      case CrowdLevel.medium:
        return Colors.orange[100]!;
      case CrowdLevel.high:
        return Colors.red[100]!;
    }
  }

  Color _getCrowdTextColor(CrowdLevel level) {
    switch (level) {
      case CrowdLevel.low:
        return Colors.green[800]!;
      case CrowdLevel.medium:
        return Colors.orange[800]!;
      case CrowdLevel.high:
        return Colors.red[800]!;
    }
  }

  String _getCrowdText(CrowdLevel level) {
    switch (level) {
      case CrowdLevel.low:
        return 'Low Crowd';
      case CrowdLevel.medium:
        return 'Medium Crowd';
      case CrowdLevel.high:
        return 'High Crowd';
    }
  }

  IconData _getFacilityIcon(String facility) {
    switch (facility.toLowerCase()) {
      case 'parking':
        return Icons.local_parking;
      case 'restroom':
        return Icons.wc;
      case 'medical':
        return Icons.medical_services;
      case 'food':
        return Icons.restaurant;
      default:
        return Icons.info;
    }
  }

  Widget _buildBottomNavBar() {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      selectedItemColor: const Color(0xFF1976D2),
      unselectedItemColor: Colors.grey,
      currentIndex: 1, // Ghats tab is selected
      onTap: (index) {
        switch (index) {
          case 0:
            context.go('/home');
            break;
          case 1:
            // Already on ghats screen
            break;
          case 2:
            context.go('/premium');
            break;
          case 3:
            context.go('/schedule');
            break;
          case 4:
            context.go('/profile');
            break;
        }
      },
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'Home',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.waves),
          label: 'Ghats',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.star),
          label: 'Premium',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.schedule),
          label: 'Schedule',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: 'Profile',
        ),
      ],
    );
  }
}