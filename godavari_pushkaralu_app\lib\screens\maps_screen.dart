import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import '../services/maps_service.dart';
import '../services/localization_service.dart';
import '../services/language_service.dart';
import '../constants/theme.dart';

class MapsScreen extends StatefulWidget {
  const MapsScreen({super.key});

  @override
  State<MapsScreen> createState() => _MapsScreenState();
}

class _MapsScreenState extends State<MapsScreen> with TickerProviderStateMixin {
  final MapsService _mapsService = MapsService();
  final LanguageService _languageService = LanguageService();
  
  GoogleMapController? _mapController;
  Position? _currentPosition;
  Map<String, GhatData> _ghatData = {};
  String? _selectedGhatId;
  bool _isLoading = true;
  
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  Set<Marker> _markers = {};
  Set<Circle> _circles = {};

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeMaps();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _pulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _initializeMaps() async {
    await _mapsService.initialize();
    
    // Listen to location updates
    _mapsService.locationStream.listen((position) {
      if (mounted) {
        setState(() {
          _currentPosition = position;
        });
        _updateMarkers();
      }
    });
    
    // Listen to ghat data updates
    _mapsService.ghatDataStream.listen((ghatData) {
      if (mounted) {
        setState(() {
          _ghatData = ghatData;
          _isLoading = false;
        });
        _updateMarkers();
      }
    });
  }

  void _updateMarkers() {
    Set<Marker> markers = {};
    Set<Circle> circles = {};
    
    // Add user location marker
    if (_currentPosition != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('user_location'),
          position: LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: InfoWindow(
            title: LocalizationService.t('your_location', _languageService.currentLanguageCode),
          ),
        ),
      );
    }
    
    // Add ghat markers
    for (String ghatId in _ghatData.keys) {
      final ghatData = _ghatData[ghatId]!;
      final ghat = ghatData.ghat;
      
      // Choose marker color based on crowd level
      double hue;
      switch (ghatData.crowdLevel) {
        case CrowdLevel.low:
          hue = BitmapDescriptor.hueGreen;
          break;
        case CrowdLevel.medium:
          hue = BitmapDescriptor.hueOrange;
          break;
        case CrowdLevel.high:
          hue = BitmapDescriptor.hueRed;
          break;
      }
      
      markers.add(
        Marker(
          markerId: MarkerId(ghatId),
          position: ghat.coordinates,
          icon: BitmapDescriptor.defaultMarkerWithHue(hue),
          infoWindow: InfoWindow(
            title: ghat.name,
            snippet: '${_getCrowdLevelText(ghatData.crowdLevel)} • ${ghatData.distanceKm?.toStringAsFixed(1) ?? '?'} km',
            onTap: () => _showGhatDetails(ghatId),
          ),
          onTap: () => _selectGhat(ghatId),
        ),
      );
      
      // Add crowd level circle
      circles.add(
        Circle(
          circleId: CircleId('${ghatId}_crowd'),
          center: ghat.coordinates,
          radius: _getCrowdRadius(ghatData.crowdLevel),
          fillColor: _getCrowdColor(ghatData.crowdLevel).withOpacity(0.2),
          strokeColor: _getCrowdColor(ghatData.crowdLevel),
          strokeWidth: 2,
        ),
      );
    }
    
    setState(() {
      _markers = markers;
      _circles = circles;
    });
  }

  double _getCrowdRadius(CrowdLevel level) {
    switch (level) {
      case CrowdLevel.low:
        return 200;
      case CrowdLevel.medium:
        return 400;
      case CrowdLevel.high:
        return 600;
    }
  }

  Color _getCrowdColor(CrowdLevel level) {
    switch (level) {
      case CrowdLevel.low:
        return Colors.green;
      case CrowdLevel.medium:
        return Colors.orange;
      case CrowdLevel.high:
        return Colors.red;
    }
  }

  String _getCrowdLevelText(CrowdLevel level) {
    switch (level) {
      case CrowdLevel.low:
        return LocalizationService.t('low_crowd', _languageService.currentLanguageCode);
      case CrowdLevel.medium:
        return LocalizationService.t('medium_crowd', _languageService.currentLanguageCode);
      case CrowdLevel.high:
        return LocalizationService.t('high_crowd', _languageService.currentLanguageCode);
    }
  }

  void _selectGhat(String ghatId) {
    setState(() {
      _selectedGhatId = ghatId;
    });
  }

  void _showGhatDetails(String ghatId) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildGhatDetailsSheet(ghatId),
    );
  }

  Widget _buildGhatDetailsSheet(String ghatId) {
    final ghatData = _ghatData[ghatId];
    if (ghatData == null) return const SizedBox();
    
    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      maxChildSize: 0.9,
      minChildSize: 0.3,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: SingleChildScrollView(
            controller: scrollController,
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle bar
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                
                // Ghat header
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            ghatData.ghat.name,
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            ghatData.ghat.location,
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    _buildCrowdBadge(ghatData.crowdLevel),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                // Distance and navigation
                if (ghatData.distanceKm != null) ...[
                  Row(
                    children: [
                      Icon(Icons.location_on, color: Colors.grey[600]),
                      const SizedBox(width: 8),
                      Text('${ghatData.distanceKm!.toStringAsFixed(1)} km away'),
                      const Spacer(),
                      ElevatedButton.icon(
                        onPressed: () => _mapsService.navigateToGhat(ghatId),
                        icon: const Icon(Icons.navigation),
                        label: Text(LocalizationService.t('navigate', _languageService.currentLanguageCode)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
                
                // Weather info
                _buildWeatherCard(ghatData.weather),
                const SizedBox(height: 20),
                
                // Current events
                if (ghatData.currentEvents.isNotEmpty) ...[
                  Text(
                    LocalizationService.t('current_events', _languageService.currentLanguageCode),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  ...ghatData.currentEvents.map((event) => _buildEventCard(event)),
                  const SizedBox(height: 20),
                ],
                
                // Facilities
                Text(
                  LocalizationService.t('facilities', _languageService.currentLanguageCode),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                _buildFacilitiesGrid(ghatData.facilityStatuses),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCrowdBadge(CrowdLevel level) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getCrowdColor(level).withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: _getCrowdColor(level)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.people,
            size: 16,
            color: _getCrowdColor(level),
          ),
          const SizedBox(width: 4),
          Text(
            _getCrowdLevelText(level),
            style: TextStyle(
              color: _getCrowdColor(level),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeatherCard(WeatherData weather) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            _getWeatherIcon(weather.condition),
            size: 32,
            color: Colors.blue,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${weather.temperature}°C • ${weather.condition}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Humidity: ${weather.humidity}% • Wind: ${weather.windSpeed} km/h',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getWeatherIcon(String condition) {
    switch (condition.toLowerCase()) {
      case 'sunny':
        return Icons.wb_sunny;
      case 'partly cloudy':
        return Icons.wb_cloudy;
      case 'cloudy':
        return Icons.cloud;
      case 'rainy':
        return Icons.umbrella;
      default:
        return Icons.wb_sunny;
    }
  }

  Widget _buildEventCard(LiveEvent event) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: event.isLive ? Colors.red.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: event.isLive ? Colors.red : Colors.grey,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          if (event.isLive) ...[
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(_pulseAnimation.value),
                    shape: BoxShape.circle,
                  ),
                );
              },
            ),
            const SizedBox(width: 8),
            Text(
              'LIVE',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  event.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  event.description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFacilitiesGrid(Map<String, FacilityStatus> facilities) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: facilities.length,
      itemBuilder: (context, index) {
        final entry = facilities.entries.elementAt(index);
        return _buildFacilityCard(entry.key, entry.value);
      },
    );
  }

  Widget _buildFacilityCard(String facility, FacilityStatus status) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: _getFacilityColor(status.availability).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _getFacilityColor(status.availability)),
      ),
      child: Row(
        children: [
          Icon(
            _getFacilityIcon(facility),
            size: 16,
            color: _getFacilityColor(status.availability),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _getFacilityName(facility),
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  status.status,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getFacilityColor(double availability) {
    if (availability > 0.8) return Colors.green;
    if (availability > 0.6) return Colors.orange;
    return Colors.red;
  }

  IconData _getFacilityIcon(String facility) {
    switch (facility) {
      case 'parking':
        return Icons.local_parking;
      case 'restroom':
        return Icons.wc;
      case 'medical':
        return Icons.medical_services;
      case 'food':
        return Icons.restaurant;
      case 'temple_access':
        return Icons.temple_hindu;
      case 'educational_tours':
        return Icons.school;
      default:
        return Icons.info;
    }
  }

  String _getFacilityName(String facility) {
    return LocalizationService.t(facility, _languageService.currentLanguageCode);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocalizationService.t('maps', _languageService.currentLanguageCode)),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.my_location),
            onPressed: _centerOnUserLocation,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Stack(
              children: [
                // Fallback map view when Google Maps is not available
                Container(
                  color: Colors.blue[50],
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.map,
                          size: 100,
                          color: Colors.blue[300],
                        ),
                        const SizedBox(height: 20),
                        Text(
                          'Interactive Maps',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[800],
                          ),
                        ),
                        const SizedBox(height: 10),
                        Text(
                          'Real-time Ghat information',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.blue[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Ghat cards overlay
                Positioned(
                  bottom: 20,
                  left: 20,
                  right: 20,
                  child: Container(
                    height: 200,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _ghatData.length,
                      itemBuilder: (context, index) {
                        final ghatId = _ghatData.keys.elementAt(index);
                        final ghatData = _ghatData[ghatId]!;
                        return Container(
                          width: 300,
                          margin: const EdgeInsets.only(right: 16),
                          child: _buildGhatCard(ghatId, ghatData),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (_selectedGhatId != null) ...[
            FloatingActionButton.extended(
              onPressed: () => _showGhatDetails(_selectedGhatId!),
              label: Text(LocalizationService.t('details', _languageService.currentLanguageCode)),
              icon: const Icon(Icons.info),
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            const SizedBox(height: 16),
          ],
          FloatingActionButton(
            onPressed: _centerOnUserLocation,
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            child: const Icon(Icons.my_location),
          ),
        ],
      ),
    );
  }

  Widget _buildGhatCard(String ghatId, GhatData ghatData) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              _getCrowdColor(ghatData.crowdLevel).withValues(alpha: 0.1),
              _getCrowdColor(ghatData.crowdLevel).withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    ghatData.ghat.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                _buildCrowdBadge(ghatData.crowdLevel),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              ghatData.ghat.location,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 12),
            if (ghatData.distanceKm != null)
              Row(
                children: [
                  Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${ghatData.distanceKm!.toStringAsFixed(1)} km away',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _mapsService.navigateToGhat(ghatId),
                    icon: const Icon(Icons.navigation, size: 16),
                    label: Text(LocalizationService.t('navigate', _languageService.currentLanguageCode)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _showGhatDetails(ghatId),
                  icon: const Icon(Icons.info_outline),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.grey[200],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _centerOnUserLocation() {
    if (_currentPosition != null && _mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(
          LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          15,
        ),
      );
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }
}
