import 'dart:async';
import 'dart:math' show cos, sqrt, asin;
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:godavari_pushkaralu_app/constants/theme.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shimmer/shimmer.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with SingleTickerProviderStateMixin {
  late Timer _timer;
  late ScrollController _scrollController;
  Duration _remaining = const Duration();
  final DateTime festivalStart = DateTime(2027, 7, 23, 0, 0, 0);
  String _selectedLanguage = 'English';
  bool _loading = true;

  // Daily Puja audio
  final AudioPlayer _player = AudioPlayer();
  bool _isPujaPlaying = false;
  double _pujaProgress = 0.0;
  Duration _pujaDuration = Duration.zero;
  Duration _pujaPosition = Duration.zero;

  // Geolocation
  Position? _position;
  String _restroomDist = '…';
  String _medicalDist = '…';
  String _waterDist = '…';

  // Seva Tracker storage
  List<Map<String, dynamic>> _sevas = [];

  // Animation controllers
  late AnimationController _contentController;
  late Animation<double> _contentAnimation;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _initCountdown();
    _initAudio();
    _initLocation();
    _loadSevas();
    
    // Initialize animation controller
    _contentController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _contentAnimation = CurvedAnimation(
      parent: _contentController,
      curve: Curves.easeInOut,
    );
    
    // Start animation after loading
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() => _loading = false);
        _contentController.forward();
      }
    });
  }

  @override
  void dispose() {
    _contentController.dispose();
    _scrollController.dispose();
    _timer.cancel();
    _player.dispose();
    super.dispose();
  }

  void _initCountdown() {
    _calculateRemaining();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) => _calculateRemaining());
  }

  void _initAudio() {
    _player.onDurationChanged.listen((d) {
      if (!mounted) return;
      setState(() => _pujaDuration = d);
    });
    _player.onPositionChanged.listen((p) {
      if (!mounted) return;
      setState(() {
        _pujaPosition = p;
        _pujaProgress = _pujaDuration.inMilliseconds == 0
            ? 0
            : _pujaPosition.inMilliseconds / _pujaDuration.inMilliseconds;
      });
    });
    _player.onPlayerComplete.listen((_) {
      if (!mounted) return;
      setState(() {
        _isPujaPlaying = false;
        _pujaProgress = 1.0;
      });
    });
  }

  Future<void> _initLocation() async {
    final hasPerm = await _ensureLocationPerms();
    if (!hasPerm) return;
    final pos = await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.medium);
    if (!mounted) return;
    setState(() => _position = pos);
    // Mock nearby facility coords around Rajahmundry
    _restroomDist = _formatDistance(_distanceKm(pos.latitude, pos.longitude, 17.0060, 81.7790));
    _medicalDist = _formatDistance(_distanceKm(pos.latitude, pos.longitude, 17.0040, 81.7860));
    _waterDist = _formatDistance(_distanceKm(pos.latitude, pos.longitude, 17.0080, 81.7835));
  }

  Future<bool> _ensureLocationPerms() async {
    if (!await Geolocator.isLocationServiceEnabled()) return false;
    var perm = await Geolocator.checkPermission();
    if (perm == LocationPermission.denied) {
      perm = await Geolocator.requestPermission();
    }
    return perm == LocationPermission.always || perm == LocationPermission.whileInUse;
  }

  String _formatDistance(double km) => km < 1 ? '${(km * 1000).round()}m' : '${km.toStringAsFixed(1)} km';
  double _distanceKm(double lat1, double lon1, double lat2, double lon2) {
    const p = 0.017453292519943295; // pi/180
    final a = 0.5 - cos((lat2 - lat1) * p) / 2 +
        cos(lat1 * p) * cos(lat2 * p) * (1 - cos((lon2 - lon1) * p)) / 2;
    return 12742 * asin(sqrt(a));
  }

  Future<void> _loadSevas() async {
    final prefs = await SharedPreferences.getInstance();
    final raw = prefs.getStringList('sevas') ?? [];
    if (raw.isEmpty) {
      _sevas = [
        {
          'title': 'Ganga Aarti (Rajahmundry)',
          'time': 'Today • 7:00 PM',
          'status': 'Confirmed',
          'color': AppTheme.successColor.value,
          'icon': Icons.emoji_people.codePoint,
        },
        {
          'title': 'Abhishekam (Bhadrachalam)',
          'time': 'Tomorrow • 6:30 AM',
          'status': 'Scheduled',
          'color': AppTheme.secondaryColor.value,
          'icon': Icons.local_florist.codePoint,
        },
        {
          'title': 'Annadanam Seva',
          'time': 'Aug 1 • 12:30 PM',
          'status': 'Pending',
          'color': Colors.orange.value,
          'icon': Icons.restaurant.codePoint,
        },
      ];
      await _saveSevas();
    } else {
      _sevas = raw.map((s) => _decodeSeva(s)).toList();
    }
    if (mounted) setState(() {});
  }

  Future<void> _saveSevas() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('sevas', _sevas.map(_encodeSeva).toList());
  }

  String _encodeSeva(Map<String, dynamic> s) =>
      [s['title'], s['time'], s['status'], s['color'].toString(), s['icon'].toString()].join('|');
  Map<String, dynamic> _decodeSeva(String raw) {
    final parts = raw.split('|');
    return {
      'title': parts[0],
      'time': parts[1],
      'status': parts[2],
      'color': int.parse(parts[3]),
      'icon': int.parse(parts[4]),
    };
  }

  void _calculateRemaining() {
    final now = DateTime.now();
    setState(() {
      _remaining = festivalStart.difference(now).isNegative
          ? Duration.zero
          : festivalStart.difference(now);
    });
  }

  void _scrollToSection(double offset) {
    _scrollController.animateTo(
      offset,
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _togglePuja() async {
    if (_isPujaPlaying) {
      await _player.pause();
    } else {
      await _player.play(AssetSource('audio/daily_puja_mantra.mp3'));
    }
    setState(() => _isPujaPlaying = !_isPujaPlaying);
  }


  @override
  Widget build(BuildContext context) {
    final days = _remaining.inDays;
    final hours = _remaining.inHours % 24;
    final minutes = _remaining.inMinutes % 60;
    final seconds = _remaining.inSeconds % 60;

    return Scaffold(
      extendBody: true,
      body: _loading
          ? Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  Container(height: 220, width: double.infinity, color: Colors.white, margin: const EdgeInsets.only(bottom: 16)),
                  Container(height: 60, width: double.infinity, color: Colors.white, margin: const EdgeInsets.only(bottom: 16)),
                  Container(height: 120, width: double.infinity, color: Colors.white, margin: const EdgeInsets.only(bottom: 16)),
                  Container(height: 80, width: double.infinity, color: Colors.white, margin: const EdgeInsets.only(bottom: 16)),
                  Container(height: 120, width: double.infinity, color: Colors.white, margin: const EdgeInsets.only(bottom: 16)),
                ],
              ),
            )
          : Column(
        children: [
          // Header Section
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF1976D2), // Blue
                  Color(0xFF42A5F5), // Light blue
                  Color(0xFFFFEB3B), // Yellow
                ],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            shape: BoxShape.circle,
                          ),
                          child: const Center(
                            child: Text(
                              'ॐ',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Godavari Pushkaralu',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                'Sacred River Festival 2027',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.notifications_outlined,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.language,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Content Section
          Expanded(
            child: Container(
              color: const Color(0xFFF5F5F5),
              child: SingleChildScrollView(
                controller: _scrollController,
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFestivalCountdown(days, hours, minutes, seconds),
                    const SizedBox(height: 24),
                    _buildQuickActions(context),
                    const SizedBox(height: 24),
                    _buildTodaysSchedule(),
                    const SizedBox(height: 24),
                    _buildNearbyFacilities(),
                    const SizedBox(height: 24),
                    _buildRecentActivity(),
                    const SizedBox(height: 100), // Space for bottom nav
                  ],
                ),
              ),
            ),
          ),
        ],
      ),

      floatingActionButton: null,
    );
  }

  Widget _buildFestivalCountdown(int days, int hours, int minutes, int seconds) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFF9800).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.calendar_today,
                  color: Color(0xFFFF9800),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Festival Countdown',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF333333),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              _buildCountdownItem('1', 'Years', const Color(0xFF2196F3)),
              _buildCountdownItem('11', 'Months', const Color(0xFFFF9800)),
              _buildCountdownItem('15', 'Days', const Color(0xFFFFC107)),
              _buildCountdownItem('6', 'Hours', const Color(0xFF4CAF50)),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            '713 days remaining until the sacred festival begins',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF666666),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCountdownItem(String value, String label, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF666666),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageChips() {
    final languages = ['English', 'తెలుగు', 'हिंदी', 'தமிழ்'];
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: languages.map((lang) {
        final selected = _selectedLanguage == lang;
        return ChoiceChip(
          label: Text(lang),
          selected: selected,
          onSelected: (_) => setState(() => _selectedLanguage = lang),
          selectedColor: AppTheme.primaryColor.withValues(alpha: 0.15),
          labelStyle: TextStyle(
            color: selected ? AppTheme.primaryColor : AppTheme.textSecondary,
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          side: BorderSide(color: Colors.black.withValues(alpha: 0.08)),
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        );
      }).toList(),
    );
  }

  Widget _buildCountdownRow(int d, int h, int m, int s) {
    Widget unit(String value, String label) => Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.15),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  )),
              Text(label,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 10,
                  )),
            ],
          ),
        );

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        unit(d.toString().padLeft(3, '0'), 'DAYS'),
        const SizedBox(width: 6),
        unit(h.toString().padLeft(2, '0'), 'HRS'),
        const SizedBox(width: 6),
        unit(m.toString().padLeft(2, '0'), 'MIN'),
        const SizedBox(width: 6),
        unit(s.toString().padLeft(2, '0'), 'SEC'),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                icon: Icons.waves,
                title: 'Holy Dip',
                subtitle: 'Plan sacred bath',
                color: const Color(0xFF2196F3),
                onTap: () => context.go('/ghats'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                icon: Icons.account_balance,
                title: 'Book Puja',
                subtitle: 'Temple rituals',
                color: const Color(0xFFFF9800),
                onTap: () => context.go('/premium'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                icon: Icons.people,
                title: 'Queue Status',
                subtitle: 'Live updates',
                color: const Color(0xFF4CAF50),
                onTap: () => _scrollToSection(600), // Scroll to schedule section
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                icon: Icons.emergency,
                title: 'Emergency',
                subtitle: 'Safety & Support',
                color: Colors.red,
                onTap: () => context.go('/emergency'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                icon: Icons.local_hospital,
                title: 'Medical',
                subtitle: 'Health services',
                color: const Color(0xFFE91E63),
                onTap: () => context.go('/medical'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                icon: Icons.cloud,
                title: 'Weather',
                subtitle: 'Forecast & alerts',
                color: const Color(0xFF03A9F4),
                onTap: () => context.go('/weather'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Color(0xFF333333),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF666666),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTodaysSchedule() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Today\'s Schedule',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF333333),
              ),
            ),
            TextButton(
              onPressed: () {},
              child: const Text(
                'View All',
                style: TextStyle(
                  color: Color(0xFF2196F3),
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildScheduleItem(
          icon: Icons.wb_sunny,
          title: 'Morning Aarti',
          time: '6:00 AM - All Temples',
          status: 'Live',
          statusColor: const Color(0xFF4CAF50),
          iconColor: const Color(0xFFFF9800),
        ),
        const SizedBox(height: 12),
        _buildScheduleItem(
          icon: Icons.waves,
          title: 'Sacred Bath Time',
          time: '7:00 AM - 11:00 AM',
          status: 'Optional',
          statusColor: const Color(0xFF2196F3),
          iconColor: const Color(0xFF2196F3),
        ),
      ],
    );
  }

  Widget _buildScheduleItem({
    required IconData icon,
    required String title,
    required String time,
    required String status,
    required Color statusColor,
    required Color iconColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  time,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              status,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNearbyFacilities() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Nearby Facilities',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Your Location',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF333333),
                    ),
                  ),
                  TextButton(
                    onPressed: () {},
                    child: const Text(
                      'Update',
                      style: TextStyle(
                        color: Color(0xFF2196F3),
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  _buildFacilityItem(
                    icon: Icons.local_hospital,
                    title: 'Medical',
                    distance: '200m',
                    color: const Color(0xFFE53935),
                  ),
                  const SizedBox(width: 16),
                  _buildFacilityItem(
                    icon: Icons.wc,
                    title: 'Restroom',
                    distance: '150m',
                    color: const Color(0xFF2196F3),
                  ),
                  const SizedBox(width: 16),
                  _buildFacilityItem(
                    icon: Icons.water_drop,
                    title: 'Water',
                    distance: '80m',
                    color: const Color(0xFF00BCD4),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFacilityItem({
    required IconData icon,
    required String title,
    required String distance,
    required Color color,
  }) {
    return Expanded(
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 2),
          Text(
            distance,
            style: const TextStyle(
              fontSize: 11,
              color: Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recent Activity',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 16),
        _buildActivityItem(
          icon: Icons.check_circle,
          title: 'Puja Booking Confirmed',
          subtitle: 'Saraswati Temple • Tomorrow 9:00 AM',
          status: 'Confirmed',
          statusColor: const Color(0xFF4CAF50),
          iconColor: const Color(0xFF4CAF50),
        ),
        const SizedBox(height: 12),
        _buildActivityItem(
          icon: Icons.volunteer_activism,
          title: 'Seva Completed',
          subtitle: 'Food distribution at Ghat 3',
          status: '+50 Points',
          statusColor: const Color(0xFFFF9800),
          iconColor: const Color(0xFFFF9800),
        ),
      ],
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required String status,
    required Color statusColor,
    required Color iconColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
          Text(
            status,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: statusColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _featureCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    String? assetPath,
    IconData? icon,
    required Color color,
    required String route,
  }) {
    return InkWell(
      borderRadius: BorderRadius.circular(12),
      onTap: () => context.go(route),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(4),
              ),
              child: assetPath != null
                ? Image.asset(
                    assetPath,
                    width: 12,
                    height: 12,
                    color: color,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(icon ?? Icons.help_outline, color: color, size: 12);
                    },
                  )
                : Icon(icon ?? Icons.help_outline, color: color, size: 12),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w700,
                color: Colors.black,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 1),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 9,
                color: Colors.black54,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyPujaCard() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.local_fire_department,
                  size: 16,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Daily Puja • Morning Mantra',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    Text('“Om Namo Bhagavate Vāsudevāya”', style: Theme.of(context).textTheme.bodyMedium),
                  ],
                ),
              ),
              IconButton(
                onPressed: _togglePuja,
                icon: Icon(_isPujaPlaying ? Icons.pause_circle_filled : Icons.play_circle_fill,
                    size: 34, color: AppTheme.primaryColor),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: LinearProgressIndicator(
              value: _pujaProgress.isNaN ? 0 : _pujaProgress.clamp(0, 1),
              minHeight: 8,
              backgroundColor: Colors.black.withValues(alpha: 0.06),
              valueColor: const AlwaysStoppedAnimation(AppTheme.primaryColor),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _isPujaPlaying
                    ? 'Playing ${('${_pujaPosition.inMinutes.remainder(60).toString().padLeft(2,'0')}:${_pujaPosition.inSeconds.remainder(60).toString().padLeft(2,'0')}')}'
                    : (_pujaProgress >= 1 ? 'Completed' : 'Ready'),
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Text('${(_pujaProgress * 100).toInt()}%', style: Theme.of(context).textTheme.bodySmall),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFacilitiesStrip() {
    Widget pill(String asset, String title, String subtitle, Color color) {
      return Expanded(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Image.asset(
                  asset,
                  width: 10,
                  height: 10,
                  color: color,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(_getIconForAsset(asset), color: color, size: 10);
                  },
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w700,
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 8,
                        color: Colors.black54,
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Row(
      children: [
        pill('assets/icons/A generic restroom toilet icon.png', 'Restrooms', _position == null ? 'Locating…' : '$_restroomDist • Clean', Colors.teal),
        const SizedBox(width: 8),
        pill('assets/icons/Medical first aid icon.png', 'Medical', _position == null ? 'Locating…' : '$_medicalDist • 24x7', Colors.red),
        const SizedBox(width: 8),
        pill('assets/icons/Drinking water icon.png', 'Water', _position == null ? 'Locating…' : '$_waterDist • Cool', Colors.blue),
      ],
    );
  }

  Widget _buildSevaTracker() {
    Color c(int v) => Color(v);
    IconData i(int v) => IconData(v, fontFamily: 'MaterialIcons');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Seva Tracker',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 6),
        ..._sevas.map((s) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: ListTile(
                dense: true,
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                leading: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: c(s['color'] as int).withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(i(s['icon'] as int), color: c(s['color'] as int), size: 16),
                ),
                title: Text(
                  s['title'] as String,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                subtitle: Text(
                  s['time'] as String,
                  style: const TextStyle(
                    fontSize: 10,
                    color: Colors.black54,
                  ),
                ),
                trailing: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                  decoration: BoxDecoration(
                    color: c(s['color'] as int).withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    s['status'] as String,
                    style: TextStyle(
                      color: c(s['color'] as int),
                      fontWeight: FontWeight.w600,
                      fontSize: 9,
                    ),
                  ),
                ),
                onTap: () {},
              ),
            )),
      ],
    );
  }

  Widget _buildLatestUpdates() {
    return Column(
      children: List.generate(3, (index) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: ListTile(
            dense: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            leading: Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.campaign, color: AppTheme.primaryColor, size: 16),
            ),
            title: Text(
              'Update ${index + 1}',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
            subtitle: const Text(
              'Important festival announcement and information.',
              style: TextStyle(
                fontSize: 10,
                color: Colors.black54,
              ),
            ),
            trailing: const Icon(Icons.chevron_right, size: 16, color: Colors.grey),
            onTap: () {},
          ),
        );
      }),
    );
  }

  IconData _getIconForAsset(String asset) {
    if (asset.contains('restroom') || asset.contains('toilet')) {
      return Icons.wc;
    } else if (asset.contains('medical') || asset.contains('first aid')) {
      return Icons.medical_services;
    } else if (asset.contains('water') || asset.contains('drinking')) {
      return Icons.water_drop;
    } else {
      return Icons.help_outline;
    }
  }

}