import 'dart:async';
import 'dart:math' show cos, sqrt, asin;
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:godavari_pushkaralu_app/constants/theme.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shimmer/shimmer.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with SingleTickerProviderStateMixin {
  late Timer _timer;
  Duration _remaining = const Duration();
  final DateTime festivalStart = DateTime(2027, 7, 23, 0, 0, 0);
  String _selectedLanguage = 'English';
  bool _loading = true;

  // Daily Puja audio
  final AudioPlayer _player = AudioPlayer();
  bool _isPujaPlaying = false;
  double _pujaProgress = 0.0;
  Duration _pujaDuration = Duration.zero;
  Duration _pujaPosition = Duration.zero;

  // Geolocation
  Position? _position;
  String _restroomDist = '…';
  String _medicalDist = '…';
  String _waterDist = '…';

  // Seva Tracker storage
  List<Map<String, dynamic>> _sevas = [];

  // Animation controllers
  late AnimationController _contentController;
  late Animation<double> _contentAnimation;

  @override
  void initState() {
    super.initState();
    _initCountdown();
    _initAudio();
    _initLocation();
    _loadSevas();
    
    // Initialize animation controller
    _contentController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _contentAnimation = CurvedAnimation(
      parent: _contentController,
      curve: Curves.easeInOut,
    );
    
    // Start animation after loading
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() => _loading = false);
        _contentController.forward();
      }
    });
  }

  @override
  void dispose() {
    _contentController.dispose();
    _timer.cancel();
    _player.dispose();
    super.dispose();
  }

  void _initCountdown() {
    _calculateRemaining();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) => _calculateRemaining());
  }

  void _initAudio() {
    _player.onDurationChanged.listen((d) {
      if (!mounted) return;
      setState(() => _pujaDuration = d);
    });
    _player.onPositionChanged.listen((p) {
      if (!mounted) return;
      setState(() {
        _pujaPosition = p;
        _pujaProgress = _pujaDuration.inMilliseconds == 0
            ? 0
            : _pujaPosition.inMilliseconds / _pujaDuration.inMilliseconds;
      });
    });
    _player.onPlayerComplete.listen((_) {
      if (!mounted) return;
      setState(() {
        _isPujaPlaying = false;
        _pujaProgress = 1.0;
      });
    });
  }

  Future<void> _initLocation() async {
    final hasPerm = await _ensureLocationPerms();
    if (!hasPerm) return;
    final pos = await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.medium);
    if (!mounted) return;
    setState(() => _position = pos);
    // Mock nearby facility coords around Rajahmundry
    _restroomDist = _formatDistance(_distanceKm(pos.latitude, pos.longitude, 17.0060, 81.7790));
    _medicalDist = _formatDistance(_distanceKm(pos.latitude, pos.longitude, 17.0040, 81.7860));
    _waterDist = _formatDistance(_distanceKm(pos.latitude, pos.longitude, 17.0080, 81.7835));
  }

  Future<bool> _ensureLocationPerms() async {
    if (!await Geolocator.isLocationServiceEnabled()) return false;
    var perm = await Geolocator.checkPermission();
    if (perm == LocationPermission.denied) {
      perm = await Geolocator.requestPermission();
    }
    return perm == LocationPermission.always || perm == LocationPermission.whileInUse;
  }

  String _formatDistance(double km) => km < 1 ? '${(km * 1000).round()}m' : '${km.toStringAsFixed(1)} km';
  double _distanceKm(double lat1, double lon1, double lat2, double lon2) {
    const p = 0.017453292519943295; // pi/180
    final a = 0.5 - cos((lat2 - lat1) * p) / 2 +
        cos(lat1 * p) * cos(lat2 * p) * (1 - cos((lon2 - lon1) * p)) / 2;
    return 12742 * asin(sqrt(a));
  }

  Future<void> _loadSevas() async {
    final prefs = await SharedPreferences.getInstance();
    final raw = prefs.getStringList('sevas') ?? [];
    if (raw.isEmpty) {
      _sevas = [
        {
          'title': 'Ganga Aarti (Rajahmundry)',
          'time': 'Today • 7:00 PM',
          'status': 'Confirmed',
          'color': AppTheme.successColor.value,
          'icon': Icons.emoji_people.codePoint,
        },
        {
          'title': 'Abhishekam (Bhadrachalam)',
          'time': 'Tomorrow • 6:30 AM',
          'status': 'Scheduled',
          'color': AppTheme.secondaryColor.value,
          'icon': Icons.local_florist.codePoint,
        },
        {
          'title': 'Annadanam Seva',
          'time': 'Aug 1 • 12:30 PM',
          'status': 'Pending',
          'color': Colors.orange.value,
          'icon': Icons.restaurant.codePoint,
        },
      ];
      await _saveSevas();
    } else {
      _sevas = raw.map((s) => _decodeSeva(s)).toList();
    }
    if (mounted) setState(() {});
  }

  Future<void> _saveSevas() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('sevas', _sevas.map(_encodeSeva).toList());
  }

  String _encodeSeva(Map<String, dynamic> s) =>
      [s['title'], s['time'], s['status'], s['color'].toString(), s['icon'].toString()].join('|');
  Map<String, dynamic> _decodeSeva(String raw) {
    final parts = raw.split('|');
    return {
      'title': parts[0],
      'time': parts[1],
      'status': parts[2],
      'color': int.parse(parts[3]),
      'icon': int.parse(parts[4]),
    };
  }

  void _calculateRemaining() {
    final now = DateTime.now();
    setState(() {
      _remaining = festivalStart.difference(now).isNegative
          ? Duration.zero
          : festivalStart.difference(now);
    });
  }

  Future<void> _togglePuja() async {
    if (_isPujaPlaying) {
      await _player.pause();
    } else {
      await _player.play(AssetSource('audio/daily_puja_mantra.mp3'));
    }
    setState(() => _isPujaPlaying = !_isPujaPlaying);
  }


  @override
  Widget build(BuildContext context) {
    final days = _remaining.inDays;
    final hours = _remaining.inHours % 24;
    final minutes = _remaining.inMinutes % 60;
    final seconds = _remaining.inSeconds % 60;

    return Scaffold(
      extendBody: true,
      body: _loading
          ? Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  Container(height: 220, width: double.infinity, color: Colors.white, margin: const EdgeInsets.only(bottom: 16)),
                  Container(height: 60, width: double.infinity, color: Colors.white, margin: const EdgeInsets.only(bottom: 16)),
                  Container(height: 120, width: double.infinity, color: Colors.white, margin: const EdgeInsets.only(bottom: 16)),
                  Container(height: 80, width: double.infinity, color: Colors.white, margin: const EdgeInsets.only(bottom: 16)),
                  Container(height: 120, width: double.infinity, color: Colors.white, margin: const EdgeInsets.only(bottom: 16)),
                ],
              ),
            )
          : Stack(
        children: [
          // Background gradient with river/temple motif placeholder
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF0D47A1), // Deep blue (river)
                  Color(0xFF42A5F5), // Lighter blue
                  Color(0xFFFFD700), // Gold
                  Color(0xFFFFA726), // Saffron
                ],
              ),
            ),
            child: Align(
              alignment: Alignment.bottomCenter,
              child: Opacity(
                opacity: 0.18,
                child: Image.asset(
                  'assets/icons/A spiritual Indian style.png',
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: 320,
                ),
              ),
            ),
          ),
          CustomScrollView(
            slivers: [
              SliverAppBar(
                pinned: true,
                expandedHeight: 260,
                backgroundColor: Colors.transparent,
                flexibleSpace: FlexibleSpaceBar(
                  title: const Text('Godavari Pushkaralu 2027'),
                  background: Container(
                    decoration: BoxDecoration(gradient: AppTheme.primaryGradient),
                    child: Stack(
                      children: [
                        // TODO: Add top banner image/illustration here
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Image.asset(
                              'assets/icons/placeholder_app_logo.png',
                              height: 48,
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 12.0),
                            child: _buildCountdownRow(days, hours, minutes, seconds),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      FadeTransition(
                        opacity: _contentAnimation,
                        child: _buildLanguageChips(),
                      ),
                      const SizedBox(height: 20),
                      FadeTransition(
                        opacity: _contentAnimation,
                        child: _buildQuickActions(context),
                      ),
                      const SizedBox(height: 20),
                      FadeTransition(
                        opacity: _contentAnimation,
                        child: _buildDailyPujaCard(),
                      ),
                      const SizedBox(height: 20),
                      FadeTransition(
                        opacity: _contentAnimation,
                        child: _buildFacilitiesStrip(),
                      ),
                      const SizedBox(height: 20),
                      FadeTransition(
                        opacity: _contentAnimation,
                        child: _buildSevaTracker(),
                      ),
                      const SizedBox(height: 20),
                      FadeTransition(
                        opacity: _contentAnimation,
                        child: Text('Latest Updates', style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
                      ),
                      const SizedBox(height: 10),
                      FadeTransition(
                        opacity: _contentAnimation,
                        child: _buildLatestUpdates(),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomNavBar(),
    );
  }

  Widget _buildLanguageChips() {
    final languages = ['English', 'తెలుగు', 'हिंदी', 'தமிழ்'];
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: languages.map((lang) {
        final selected = _selectedLanguage == lang;
        return ChoiceChip(
          label: Text(lang),
          selected: selected,
          onSelected: (_) => setState(() => _selectedLanguage = lang),
          selectedColor: AppTheme.primaryColor.withOpacity(0.15),
          labelStyle: TextStyle(
            color: selected ? AppTheme.primaryColor : AppTheme.textSecondary,
            fontWeight: FontWeight.w600,
          ),
          side: BorderSide(color: Colors.black.withOpacity(0.08)),
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        );
      }).toList(),
    );
  }

  Widget _buildCountdownRow(int d, int h, int m, int s) {
    Widget unit(String value, String label) => Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  )),
              Text(label,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 12,
                  )),
            ],
          ),
        );

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        unit(d.toString().padLeft(2, '0'), 'DAYS'),
        const SizedBox(width: 8),
        unit(h.toString().padLeft(2, '0'), 'HRS'),
        const SizedBox(width: 8),
        unit(m.toString().padLeft(2, '0'), 'MIN'),
        const SizedBox(width: 8),
        unit(s.toString().padLeft(2, '0'), 'SEC'),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 12,
      crossAxisSpacing: 12,
      childAspectRatio: 1.2,
      children: [
        _featureCard(
          context: context,
          title: 'Holy Dip Planner',
          subtitle: 'Best time & safe zones',
          icon: Icons.water_drop,
          color: AppTheme.primaryColor,
          route: '/ghats',
        ),
        _featureCard(
          context: context,
          title: 'Puja Booking',
          subtitle: 'Rituals & darshan',
          icon: Icons.wb_incandescent,
          color: AppTheme.secondaryColor,
          route: '/premium',
        ),
        _featureCard(
          context: context,
          title: 'Queue Status',
          subtitle: 'Live crowd updates',
          icon: Icons.query_stats,
          color: Colors.green,
          route: '/updates',
        ),
        _featureCard(
          context: context,
          title: 'Accessibility',
          subtitle: 'Elderly & wheelchair',
          icon: Icons.accessibility_new,
          color: Colors.purple,
          route: '/ghats',
        ),
      ],
    );
  }

  Widget _featureCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required String route,
  }) {
    return InkWell(
      borderRadius: BorderRadius.circular(16),
      onTap: () => context.go(route),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [AppTheme.softShadow],
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: color.withOpacity(0.12),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const Spacer(),
            Text(title, style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 4),
            Text(subtitle, style: Theme.of(context).textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyPujaCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppTheme.softShadow],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  gradient: AppTheme.goldGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Image.asset(
                  'assets/icons/placeholder_diya.png',
                  height: 20,
                  width: 20,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Daily Puja • Morning Mantra', style: Theme.of(context).textTheme.titleMedium),
                    Text('“Om Namo Bhagavate Vāsudevāya”', style: Theme.of(context).textTheme.bodyMedium),
                  ],
                ),
              ),
              IconButton(
                onPressed: _togglePuja,
                icon: Icon(_isPujaPlaying ? Icons.pause_circle_filled : Icons.play_circle_fill,
                    size: 34, color: AppTheme.primaryColor),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: LinearProgressIndicator(
              value: _pujaProgress.isNaN ? 0 : _pujaProgress.clamp(0, 1),
              minHeight: 8,
              backgroundColor: Colors.black.withOpacity(0.06),
              valueColor: const AlwaysStoppedAnimation(AppTheme.primaryColor),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _isPujaPlaying
                    ? 'Playing ${('${_pujaPosition.inMinutes.remainder(60).toString().padLeft(2,'0')}:${_pujaPosition.inSeconds.remainder(60).toString().padLeft(2,'0')}')}'
                    : (_pujaProgress >= 1 ? 'Completed' : 'Ready'),
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Text('${(_pujaProgress * 100).toInt()}%', style: Theme.of(context).textTheme.bodySmall),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFacilitiesStrip() {
    Widget pill(String asset, String title, String subtitle, Color color) {
      return Expanded(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [AppTheme.softShadow],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.12),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Image.asset(asset, color: color, height: 16, width: 16),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(title, style: Theme.of(context).textTheme.titleSmall),
                    Text(subtitle, style: Theme.of(context).textTheme.bodySmall, overflow: TextOverflow.ellipsis),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Row(
      children: [
        pill('assets/icons/placeholder_restroom.png', 'Restrooms', _position == null ? 'Locating…' : '$_restroomDist • Clean', Colors.teal),
        const SizedBox(width: 10),
        pill('assets/icons/placeholder_medical.png', 'Medical', _position == null ? 'Locating…' : '$_medicalDist • 24x7', Colors.red),
        const SizedBox(width: 10),
        pill('assets/icons/placeholder_water.png', 'Drinking Water', _position == null ? 'Locating…' : '$_waterDist • Cool', Colors.blue),
      ],
    );
  }

  Widget _buildSevaTracker() {
    Color c(int v) => Color(v);
    IconData i(int v) => IconData(v, fontFamily: 'MaterialIcons');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Seva Tracker', style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(height: 8),
        ..._sevas.map((s) => Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [AppTheme.softShadow],
              ),
              child: ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: c(s['color'] as int).withOpacity(0.12),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(i(s['icon'] as int), color: c(s['color'] as int)),
                ),
                title: Text(s['title'] as String, style: Theme.of(context).textTheme.titleMedium),
                subtitle: Text(s['time'] as String),
                trailing: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  decoration: BoxDecoration(
                    color: c(s['color'] as int).withOpacity(0.12),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    s['status'] as String,
                    style: TextStyle(color: c(s['color'] as int), fontWeight: FontWeight.w600),
                  ),
                ),
                onTap: () {},
              ),
            )),
      ],
    );
  }

  Widget _buildLatestUpdates() {
    return Column(
      children: List.generate(3, (index) {
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [AppTheme.softShadow],
          ),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.12),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.campaign, color: AppTheme.primaryColor),
            ),
            title: Text('Update ${index + 1}', style: Theme.of(context).textTheme.titleMedium),
            subtitle: const Text('Important festival announcement and information.'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {},
          ),
        );
      }),
    );
  }

  Widget _buildBottomNavBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.95),
        boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 8)],
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _navBarItem(Icons.home, 'Home', true),
          _navBarItem(Icons.temple_buddhist, 'Ghats', false),
          _navBarItem(Icons.workspace_premium, 'Premium', false),
          _navBarItem(Icons.event, 'Schedule', false),
          _navBarItem(Icons.person, 'Profile', false),
        ],
      ),
    );
  }

  Widget _navBarItem(IconData icon, String label, bool selected) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: selected ? AppTheme.primaryColor : Colors.grey, size: 28),
        const SizedBox(height: 2),
        Text(label, style: TextStyle(
          color: selected ? AppTheme.primaryColor : Colors.grey,
          fontWeight: selected ? FontWeight.bold : FontWeight.normal,
          fontSize: 12,
        )),
      ],
    );
  }
}