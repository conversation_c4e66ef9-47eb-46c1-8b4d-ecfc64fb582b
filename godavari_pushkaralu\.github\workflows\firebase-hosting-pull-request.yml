name: Deploy to Firebase Hosting on PR
'on': pull_request
jobs:
  build_and_preview:
    if: '${{ github.event.pull_request.head.repo.full_name == github.repository }}'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      # Set up Flutter
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
      
      # Get Flutter dependencies
      - name: Get dependencies
        run: |
          cd godavari_pushkaralu_app
          flutter pub get
      
      # Build web app
      - name: Build web app
        run: |
          cd godavari_pushkaralu_app
          flutter build web
      
      # Deploy to Firebase
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_GP_2K27 }}'
          projectId: gp-2k27 