import 'package:flutter/material.dart';
import 'package:godavari_pushkaralu_app/constants/theme.dart';

enum EventStatus { live, active, upcoming }
enum EventType { all, religious, cultural, ghats }

class EventInfo {
  final String title;
  final String description;
  final String startTime;
  final String endTime;
  final String location;
  final EventStatus status;
  final String crowdLevel;
  final Color crowdColor;
  final IconData locationIcon;

  EventInfo({
    required this.title,
    required this.description,
    required this.startTime,
    required this.endTime,
    required this.location,
    required this.status,
    required this.crowdLevel,
    required this.crowdColor,
    required this.locationIcon,
  });
}

class ScheduleScreen extends StatefulWidget {
  const ScheduleScreen({super.key});

  @override
  State<ScheduleScreen> createState() => _ScheduleScreenState();
}

class _ScheduleScreenState extends State<ScheduleScreen> {
  int _selectedDay = 3; // Day 3 is selected as shown in image
  String _selectedTimeFilter = 'Today';
  EventType _selectedEventType = EventType.all;

  final List<EventInfo> _todaysEvents = [
    EventInfo(
      title: 'Morning Aarti',
      description: 'Sacred morning prayers at all temples',
      startTime: '6:00 AM',
      endTime: '6:30 AM',
      location: 'All Temples',
      status: EventStatus.live,
      crowdLevel: 'Medium Crowd',
      crowdColor: Colors.orange,
      locationIcon: Icons.temple_hindu,
    ),
    EventInfo(
      title: 'Sacred Bath Time',
      description: 'Auspicious time for holy dip in Godavari',
      startTime: '7:00 AM',
      endTime: '11:00 AM',
      location: 'Main Ghats',
      status: EventStatus.active,
      crowdLevel: 'High Crowd',
      crowdColor: Colors.red,
      locationIcon: Icons.waves,
    ),
    EventInfo(
      title: 'Rudrabhishek Puja',
      description: 'Special prayers and offerings to Lord Shiva',
      startTime: '12:00 PM',
      endTime: '1:00 PM',
      location: 'Bhadrachalam Temple',
      status: EventStatus.upcoming,
      crowdLevel: 'Low Crowd',
      crowdColor: Colors.green,
      locationIcon: Icons.temple_hindu,
    ),
    EventInfo(
      title: 'Evening Prayers',
      description: 'Sandhya Aarti and evening rituals',
      startTime: '6:00 PM',
      endTime: '8:00 PM',
      location: 'All Temples',
      status: EventStatus.upcoming,
      crowdLevel: 'Medium Crowd',
      crowdColor: Colors.orange,
      locationIcon: Icons.temple_hindu,
    ),
    EventInfo(
      title: 'Cultural Programs',
      description: 'Traditional music and dance performances',
      startTime: '8:00 PM',
      endTime: '10:00 PM',
      location: 'Cultural Stage',
      status: EventStatus.upcoming,
      crowdLevel: 'High Crowd',
      crowdColor: Colors.red,
      locationIcon: Icons.theater_comedy,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: Container(
              color: const Color(0xFFF5F5F5),
              child: Column(
                children: [
                  _buildTimeFilters(),
                  _buildEventTypeFilters(),
                  _buildEventsHeader(),
                  Expanded(
                    child: _buildEventsList(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomNavBar(),
    );
  }

  Widget _buildHeader() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF1976D2),
            Color(0xFF1565C0),
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.calendar_today,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Festival Schedule',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Day 3',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Text(
                'March 15, 2027',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 20),
              _buildCalendar(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCalendar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Icon(
                Icons.chevron_left,
                color: Colors.white,
              ),
              const Text(
                'March 2027',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Icon(
                Icons.chevron_right,
                color: Colors.white,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: ['S', 'M', 'T', 'W', 'T', 'F', 'S']
                .map((day) => Text(
                      day,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontWeight: FontWeight.w600,
                      ),
                    ))
                .toList(),
          ),
          const SizedBox(height: 12),
          _buildCalendarGrid(),
        ],
      ),
    );
  }

  Widget _buildCalendarGrid() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildCalendarDay('28', false),
            _buildCalendarDay('1', false),
            _buildCalendarDay('2', false),
            _buildCalendarDay('3', false),
            _buildCalendarDay('4', false),
            _buildCalendarDay('5', false),
            _buildCalendarDay('6', false),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildCalendarDay('7', false),
            _buildCalendarDay('8', false),
            _buildCalendarDay('9', false),
            _buildCalendarDay('10', false),
            _buildCalendarDay('11', false),
            _buildCalendarDay('12', false),
            _buildCalendarDay('13', false),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildCalendarDay('14', false),
            _buildCalendarDay('15', true), // Selected day
            _buildCalendarDay('16', false),
            _buildCalendarDay('17', false),
            _buildCalendarDay('18', false),
            _buildCalendarDay('19', false),
            _buildCalendarDay('20', false),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildCalendarDay('21', false),
            _buildCalendarDay('22', false),
            _buildCalendarDay('23', false),
            _buildCalendarDay('24', false),
            _buildCalendarDay('25', false),
            _buildCalendarDay('26', false),
            _buildCalendarDay('27', false),
          ],
        ),
      ],
    );
  }

  Widget _buildCalendarDay(String day, bool isSelected) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: isSelected ? Colors.orange : Colors.transparent,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(
        child: Text(
          day,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.white70,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildTimeFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          _buildTimeFilterChip('Today', true),
          const SizedBox(width: 8),
          _buildTimeFilterChip('Tomorrow', false),
          const SizedBox(width: 8),
          _buildTimeFilterChip('This Week', false),
          const SizedBox(width: 8),
          _buildTimeFilterChip('Full', false),
        ],
      ),
    );
  }

  Widget _buildTimeFilterChip(String label, bool isSelected) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isSelected ? Colors.orange : Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isSelected ? Colors.orange : Colors.grey[300]!,
        ),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : Colors.grey[700],
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildEventTypeFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          _buildEventTypeChip('All Events', EventType.all, true),
          const SizedBox(width: 8),
          _buildEventTypeChip('Religious', EventType.religious, false),
          const SizedBox(width: 8),
          _buildEventTypeChip('Cultural', EventType.cultural, false),
          const SizedBox(width: 8),
          _buildEventTypeChip('Ghats', EventType.ghats, false),
        ],
      ),
    );
  }

  Widget _buildEventTypeChip(String label, EventType type, bool isSelected) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isSelected ? const Color(0xFF1976D2) : Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isSelected ? const Color(0xFF1976D2) : Colors.grey[300]!,
        ),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : Colors.grey[700],
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildEventsHeader() {
    return const Padding(
      padding: EdgeInsets.all(16),
      child: Row(
        children: [
          Text(
            "Today's Sacred Events",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _todaysEvents.length,
      itemBuilder: (context, index) {
        return _buildEventCard(_todaysEvents[index]);
      },
    );
  }

  Widget _buildEventCard(EventInfo event) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator
          Column(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: _getStatusColor(event.status),
                  shape: BoxShape.circle,
                ),
              ),
              if (_todaysEvents.indexOf(event) < _todaysEvents.length - 1)
                Container(
                  width: 2,
                  height: 80,
                  color: Colors.orange[200],
                ),
            ],
          ),
          const SizedBox(width: 16),
          // Event card
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          event.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(event.status),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getStatusText(event.status),
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    event.description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      const Icon(
                        Icons.access_time,
                        size: 16,
                        color: Colors.orange,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${event.startTime} - ${event.endTime}',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.orange,
                        ),
                      ),
                      const Spacer(),
                      Icon(
                        event.locationIcon,
                        size: 16,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        event.location,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: event.crowdColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.people,
                              size: 12,
                              color: event.crowdColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              event.crowdLevel,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: event.crowdColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      ElevatedButton(
                        onPressed: () {},
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF1976D2),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                        ),
                        child: Text(
                          event.status == EventStatus.live
                              ? 'Remind Me'
                              : 'Set Reminder',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(EventStatus status) {
    switch (status) {
      case EventStatus.live:
        return Colors.green;
      case EventStatus.active:
        return Colors.orange;
      case EventStatus.upcoming:
        return Colors.blue;
    }
  }

  String _getStatusText(EventStatus status) {
    switch (status) {
      case EventStatus.live:
        return 'Live';
      case EventStatus.active:
        return 'Active';
      case EventStatus.upcoming:
        return 'Upcoming';
    }
  }

  Widget _buildBottomNavBar() {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      selectedItemColor: Colors.orange,
      unselectedItemColor: Colors.grey,
      currentIndex: 3, // Schedule tab is selected
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'Home',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.waves),
          label: 'Ghats',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.star),
          label: 'Premium',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.calendar_today),
          label: 'Schedule',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: 'Profile',
        ),
      ],
    );
  }
}