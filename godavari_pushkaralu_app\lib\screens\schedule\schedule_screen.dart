import 'package:flutter/material.dart';
import 'package:godavari_pushkaralu_app/constants/theme.dart';

class ScheduleScreen extends StatefulWidget {
  const ScheduleScreen({super.key});

  @override
  State<ScheduleScreen> createState() => _ScheduleScreenState();
}

class _ScheduleScreenState extends State<ScheduleScreen> {
  int _selectedDay = 0;

  final List<Map<String, dynamic>> _schedule = [
    {
      'day': 1,
      'date': 'July 23, 2027',
      'title': 'Pushkara Aarambha',
      'events': [
        {
          'time': '6:00 AM',
          'title': 'Opening Ceremony',
          'location': 'All Ghats',
          'description': 'Sacred beginning of Godavari Pushkaralu',
          'icon': Icons.wb_sunny,
          'color': Colors.orange,
        },
        {
          'time': '12:00 PM',
          'title': 'First Ganga Aarti',
          'location': 'Rajahmundry Ghat',
          'description': 'Traditional river worship ceremony',
          'icon': Icons.water,
          'color': AppTheme.primaryColor,
        },
        {
          'time': '7:00 PM',
          'title': 'Cultural Inauguration',
          'location': 'Bhadrachalam Ghat',
          'description': 'Traditional dance and music performances',
          'icon': Icons.music_note,
          'color': Colors.purple,
        },
      ],
    },
    {
      'day': 2,
      'date': 'July 24, 2027',
      'title': 'Ganga Aarti Day',
      'events': [
        {
          'time': '5:30 AM',
          'title': 'Morning Prayers',
          'location': 'All Ghats',
          'description': 'Sacred morning rituals',
          'icon': Icons.wb_sunny,
          'color': Colors.orange,
        },
        {
          'time': '11:00 AM',
          'title': 'Youth Programs',
          'location': 'Mancherial Ghat',
          'description': 'Special activities for young devotees',
          'icon': Icons.people,
          'color': Colors.green,
        },
        {
          'time': '6:00 PM',
          'title': 'Classical Music Concert',
          'location': 'Basara Ghat',
          'description': 'Traditional Indian classical music',
          'icon': Icons.music_note,
          'color': Colors.purple,
        },
      ],
    },
    {
      'day': 3,
      'date': 'July 25, 2027',
      'title': 'Special Pujas',
      'events': [
        {
          'time': '6:00 AM',
          'title': 'Special Pujas',
          'location': 'Bhadrachalam Ghat',
          'description': 'Sacred temple ceremonies',
          'icon': Icons.temple_hindu,
          'color': AppTheme.secondaryColor,
        },
        {
          'time': '10:00 AM',
          'title': 'Rama Pattabhishekam',
          'location': 'Bhadrachalam Ghat',
          'description': 'Re-enactment of Lord Rama\'s coronation',
          'icon': Icons.auto_awesome,
          'color': Colors.amber,
        },
        {
          'time': '8:00 PM',
          'title': 'Devotional Singing',
          'location': 'All Ghats',
          'description': 'Bhajan and kirtan sessions',
          'icon': Icons.music_note,
          'color': Colors.purple,
        },
      ],
    },
    {
      'day': 4,
      'date': 'July 26, 2027',
      'title': 'Cultural Programs',
      'events': [
        {
          'time': '7:00 AM',
          'title': 'Community Breakfast',
          'location': 'All Locations',
          'description': 'Prasadam distribution',
          'icon': Icons.restaurant,
          'color': Colors.green,
        },
        {
          'time': '2:00 PM',
          'title': 'Cultural Programs',
          'location': 'Rajahmundry Ghat',
          'description': 'Traditional art performances',
          'icon': Icons.theater_comedy,
          'color': Colors.purple,
        },
        {
          'time': '7:30 PM',
          'title': 'Folk Dance',
          'location': 'Kovvur Ghat',
          'description': 'Regional folk dance performances',
          'icon': Icons.music_note,
          'color': Colors.orange,
        },
      ],
    },
    {
      'day': 5,
      'date': 'July 27, 2027',
      'title': 'Saraswati Puja',
      'events': [
        {
          'time': '6:30 AM',
          'title': 'Saraswati Puja',
          'location': 'Basara Ghat',
          'description': 'Goddess of knowledge worship',
          'icon': Icons.school,
          'color': Colors.purple,
        },
        {
          'time': '11:00 AM',
          'title': 'Educational Seminars',
          'location': 'Basara Ghat',
          'description': 'Spiritual and cultural discourses',
          'icon': Icons.library_books,
          'color': Colors.blue,
        },
        {
          'time': '5:00 PM',
          'title': 'Knowledge Competition',
          'location': 'All Ghats',
          'description': 'Quiz and cultural competitions',
          'icon': Icons.quiz,
          'color': Colors.green,
        },
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Event Schedule'),
      ),
      body: Column(
        children: [
          _buildDaySelector(),
          Expanded(
            child: _buildEventList(),
          ),
        ],
      ),
    );
  }

  Widget _buildDaySelector() {
    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _schedule.length,
        itemBuilder: (context, index) {
          final day = _schedule[index];
          final isSelected = _selectedDay == index;
          
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedDay = index;
              });
            },
            child: Container(
              width: 80,
              margin: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                color: isSelected ? AppTheme.primaryColor : Colors.grey[200],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Day ${day['day']}',
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    day['date'].split(',')[0],
                    style: TextStyle(
                      color: isSelected ? Colors.white70 : Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEventList() {
    final selectedDay = _schedule[_selectedDay];
    
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Text(
          selectedDay['title'],
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...selectedDay['events'].map<Widget>((event) => _buildEventCard(event)),
      ],
    );
  }

  Widget _buildEventCard(Map<String, dynamic> event) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: event['color'].withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                event['icon'],
                color: event['color'],
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        event['time'],
                        style: TextStyle(
                          color: event['color'],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      Icon(
                        Icons.notifications,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    event['title'],
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    event['location'],
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    event['description'],
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
} 