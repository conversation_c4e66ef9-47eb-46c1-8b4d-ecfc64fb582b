import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../services/localization_service.dart';
import '../services/language_service.dart';

class ServicesScreen extends StatefulWidget {
  const ServicesScreen({super.key});

  @override
  State<ServicesScreen> createState() => _ServicesScreenState();
}

class _ServicesScreenState extends State<ServicesScreen> {
  String get _selectedLanguage => languageService.currentLanguageCode;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0D47A1),
      appBar: AppBar(
        title: Text(
          LocalizationService.t('services', _selectedLanguage),
          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF0D47A1),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF0D47A1), Color(0xFF42A5F5), Color(0xFFFFD700)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.temple_hindu,
                        size: 48,
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        LocalizationService.t('spiritual_services', _selectedLanguage),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        LocalizationService.t('book_spiritual_services', _selectedLanguage),
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // Service Categories
                _buildServiceCategory(
                  LocalizationService.t('puja_services', _selectedLanguage),
                  Icons.auto_awesome,
                  [
                    _ServiceItem(
                      LocalizationService.t('daily_puja', _selectedLanguage),
                      LocalizationService.t('daily_puja_desc', _selectedLanguage),
                      Icons.brightness_7,
                      '₹500',
                    ),
                    _ServiceItem(
                      LocalizationService.t('special_puja', _selectedLanguage),
                      LocalizationService.t('special_puja_desc', _selectedLanguage),
                      Icons.star,
                      '₹1500',
                    ),
                    _ServiceItem(
                      LocalizationService.t('homam_service', _selectedLanguage),
                      LocalizationService.t('homam_desc', _selectedLanguage),
                      Icons.local_fire_department,
                      '₹2500',
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                _buildServiceCategory(
                  LocalizationService.t('darshan_services', _selectedLanguage),
                  Icons.visibility,
                  [
                    _ServiceItem(
                      LocalizationService.t('vip_darshan', _selectedLanguage),
                      LocalizationService.t('vip_darshan_desc', _selectedLanguage),
                      Icons.star_border,
                      '₹200',
                    ),
                    _ServiceItem(
                      LocalizationService.t('special_darshan', _selectedLanguage),
                      LocalizationService.t('special_darshan_desc', _selectedLanguage),
                      Icons.schedule,
                      '₹100',
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                _buildServiceCategory(
                  LocalizationService.t('accommodation', _selectedLanguage),
                  Icons.hotel,
                  [
                    _ServiceItem(
                      LocalizationService.t('dharamshala', _selectedLanguage),
                      LocalizationService.t('dharamshala_desc', _selectedLanguage),
                      Icons.home,
                      '₹300/night',
                    ),
                    _ServiceItem(
                      LocalizationService.t('guest_house', _selectedLanguage),
                      LocalizationService.t('guest_house_desc', _selectedLanguage),
                      Icons.house,
                      '₹800/night',
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                _buildServiceCategory(
                  LocalizationService.t('transportation', _selectedLanguage),
                  Icons.directions_bus,
                  [
                    _ServiceItem(
                      LocalizationService.t('shuttle_service', _selectedLanguage),
                      LocalizationService.t('shuttle_desc', _selectedLanguage),
                      Icons.airport_shuttle,
                      '₹50',
                    ),
                    _ServiceItem(
                      LocalizationService.t('private_vehicle', _selectedLanguage),
                      LocalizationService.t('private_vehicle_desc', _selectedLanguage),
                      Icons.car_rental,
                      '₹1200/day',
                    ),
                  ],
                ),
                const SizedBox(height: 32),

                // Emergency Services Button
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.emergency, color: Colors.white, size: 32),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              LocalizationService.t('emergency_services', _selectedLanguage),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              LocalizationService.t('emergency_desc', _selectedLanguage),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          // Handle emergency call
                          _showEmergencyDialog();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.red,
                        ),
                        child: Text(LocalizationService.t('call_now', _selectedLanguage)),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildServiceCategory(String title, IconData icon, List<_ServiceItem> services) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(icon, color: Colors.white, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          ...services.map((service) => _buildServiceTile(service)),
        ],
      ),
    );
  }

  Widget _buildServiceTile(_ServiceItem service) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: Icon(service.icon, color: Colors.white),
        title: Text(
          service.title,
          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          service.description,
          style: TextStyle(color: Colors.white.withValues(alpha: 0.8)),
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              service.price,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 4),
            ElevatedButton(
              onPressed: () => _bookService(service),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFFD700),
                foregroundColor: const Color(0xFF0D47A1),
                minimumSize: const Size(60, 30),
                padding: const EdgeInsets.symmetric(horizontal: 8),
              ),
              child: Text(
                LocalizationService.t('book', _selectedLanguage),
                style: const TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _bookService(_ServiceItem service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocalizationService.t('book_service', _selectedLanguage)),
        content: Text(
          '${LocalizationService.t('confirm_booking', _selectedLanguage)} ${service.title} ${LocalizationService.t('for', _selectedLanguage)} ${service.price}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(LocalizationService.t('cancel', _selectedLanguage)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showBookingConfirmation(service);
            },
            child: Text(LocalizationService.t('confirm', _selectedLanguage)),
          ),
        ],
      ),
    );
  }

  void _showBookingConfirmation(_ServiceItem service) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '${service.title} ${LocalizationService.t('booked_successfully', _selectedLanguage)}',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showEmergencyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocalizationService.t('emergency_services', _selectedLanguage)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.local_hospital, color: Colors.red),
              title: Text(LocalizationService.t('medical_emergency', _selectedLanguage)),
              subtitle: const Text('108'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.local_police, color: Colors.blue),
              title: Text(LocalizationService.t('police', _selectedLanguage)),
              subtitle: const Text('100'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.local_fire_department, color: Colors.orange),
              title: Text(LocalizationService.t('fire_emergency', _selectedLanguage)),
              subtitle: const Text('101'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(LocalizationService.t('close', _selectedLanguage)),
          ),
        ],
      ),
    );
  }
}

class _ServiceItem {
  final String title;
  final String description;
  final IconData icon;
  final String price;

  _ServiceItem(this.title, this.description, this.icon, this.price);
}
