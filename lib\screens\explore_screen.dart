import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class ExploreScreen extends StatelessWidget {
  const ExploreScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Explore'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Explore <PERSON><PERSON><PERSON>',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 20),
            
            // View Gallery Section
            _buildSectionCard(
              'View Gallery',
              'Browse through beautiful images of the festival',
              Icons.photo_library,
              () => context.go('/gallery'),
            ),
            
            // Nearby Attractions
            _buildSectionCard(
              'Nearby Attractions',
              'Discover places to visit around Pushkaralu',
              Icons.place,
              () => context.go('/attractions'),
            ),
            
            // Ghats Information
            _buildSectionCard(
              'Sacred Ghats',
              'Learn about the holy bathing ghats',
              Icons.water,
              () => context.go('/ghats'),
            ),
            
            // Accommodation
            _buildSectionCard(
              'Accommodation',
              'Find hotels and lodging options',
              Icons.hotel,
              () => context.go('/accommodation'),
            ),
            
            // Transportation
            _buildSectionCard(
              'Transportation',
              'Bus, train, and flight information',
              Icons.directions_bus,
              () => context.go('/transportation'),
            ),
            
            // Food & Prasadam
            _buildSectionCard(
              'Food & Prasadam',
              'Traditional food and sacred offerings',
              Icons.restaurant,
              () => context.go('/food'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(String title, String description, IconData icon, VoidCallback onTap) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: const Color(0xFF4CAF50),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
