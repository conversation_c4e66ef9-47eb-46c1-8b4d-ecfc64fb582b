'use strict';
const MANIFEST = 'flutter-app-manifest';
const TEMP = 'flutter-temp-cache';
const CACHE_NAME = 'flutter-app-cache';

const RESOURCES = {"assets/AssetManifest.bin": "b1777551b550770115a5dd0af86c246b",
"assets/AssetManifest.bin.json": "f85b2b1852adb15a0da9e0ff07f5d2de",
"assets/AssetManifest.json": "fd74644d42466fca84ff5dbd3db94feb",
"assets/assets/audio/daily_puja_mantra.mp3": "d41d8cd98f00b204e9800998ecf8427e",
"assets/assets/icons/A%2520generic%2520restroom%2520toilet%2520icon.png": "66ca6d2b6b934e261cfda929fb508058",
"assets/assets/icons/A%2520spiritual%2520Indian%2520style.png": "a2cda3b928e3ab6f0f13d36926cd1f96",
"assets/assets/icons/App%2520Logo.png": "bc9f31df03c10f5c850f5db5582b48bf",
"assets/assets/icons/Diya%2520lamp%2520icon.png": "8d35b44f7e3ed58aee9d338a35c162a9",
"assets/assets/icons/Drinking%2520water%2520icon.png": "91a614769466d1e56a31209aafc7c692",
"assets/assets/icons/Food%2520annadanam%2520icon.png": "580f573c586ee431e54097c2e938448a",
"assets/assets/icons/Godavari%2520river,%2520temple,%2520or%2520festival%2520background.%2520Use%2520a%2520beautiful,%2520spiritual%2520photo%2520or%2520illustration.webp": "6907d2c042d25fa4ffa30e78b5a6484a",
"assets/assets/icons/lotus%2520flower%2520icon.png": "0f66a9394602ed890c6a2c68a76cd0ab",
"assets/assets/icons/Medical%2520first%2520aid%2520icon.png": "461a5adba0ded1b4bedceabd83dd2242",
"assets/assets/icons/placeholder_app_logo.png": "bc949ea893a9384070c31f083ccefd26",
"assets/assets/icons/placeholder_bell.png": "bc949ea893a9384070c31f083ccefd26",
"assets/assets/icons/placeholder_bronze_badge.png": "bc949ea893a9384070c31f083ccefd26",
"assets/assets/icons/placeholder_diya.png": "bc949ea893a9384070c31f083ccefd26",
"assets/assets/icons/placeholder_food.png": "bc949ea893a9384070c31f083ccefd26",
"assets/assets/icons/placeholder_gold_badge.png": "bc949ea893a9384070c31f083ccefd26",
"assets/assets/icons/placeholder_lotus.png": "bc949ea893a9384070c31f083ccefd26",
"assets/assets/icons/placeholder_medical.png": "bc949ea893a9384070c31f083ccefd26",
"assets/assets/icons/placeholder_restroom.png": "bc949ea893a9384070c31f083ccefd26",
"assets/assets/icons/placeholder_silver_badge.png": "bc949ea893a9384070c31f083ccefd26",
"assets/assets/icons/placeholder_transport.png": "bc949ea893a9384070c31f083ccefd26",
"assets/assets/icons/placeholder_water.png": "bc949ea893a9384070c31f083ccefd26",
"assets/assets/icons/royal%2520badge%2520gold.png": "491d34de4f87bd471034935426ac73dd",
"assets/assets/icons/temple%2520bell.png": "7ab6beb7b02958a3978c71a3b9953419",
"assets/assets/icons/Transport%2520bus%2520train%2520icon.png": "46bfacc0b631590dca75effcdd663bb8",
"assets/assets/icons/vip%2520bronze.png": "8f49001365a3d9ca51067cdfe120e0b9",
"assets/assets/icons/vvip%2520silver.png": "4d413cdae94b49c5793a996ee1a4e04b",
"assets/assets/images/devotional_pattern.png": "bea1a48ba8737bf36aff27bc49ebee5d",
"assets/assets/images/ganga_aarti.jpg": "a7847f489d60bd4d33ae61e014f4c4e8",
"assets/assets/images/godavari_river.jpg": "b1cf4ceeffdff016c4dcd54ce5ffc087",
"assets/assets/images/placeholder_godavari_temple.jpg": "bc949ea893a9384070c31f083ccefd26",
"assets/assets/images/pushkar_ghat.jpg": "6decc74744bc4babf8118d49b4398bc1",
"assets/assets/images/temple_background.jpg": "ccf39a1fb33d766e47a59527a03cfcdb",
"assets/assets/lottie/Campers%2520Welcome.json": "5a5ec91841940b5dbc00456b00e458a3",
"assets/assets/lottie/Confetti.json": "ff63a9b38d34fece66ab25e011855e49",
"assets/assets/lottie/Loading%2520animation.json": "dc2ba8fc8cd3fc27f7d4073b90c327da",
"assets/assets/lottie/loading.json": "6bb5b3464b7239f39afe287691a1ae3b",
"assets/assets/lottie/onboard_anim.json": "e366ef0a9a948a745bd73469cd0313d9",
"assets/assets/lottie/success.json": "cc2907925777c3d7bbda7241a7d32fd3",
"assets/FontManifest.json": "dc3d03800ccca4601324923c0b1d6d57",
"assets/fonts/MaterialIcons-Regular.otf": "e7069dfd19b331be16bed984668fe080",
"assets/NOTICES": "e1c5100f0b282c53db040cb9570116c8",
"assets/packages/cupertino_icons/assets/CupertinoIcons.ttf": "b93248a553f9e8bc17f1065929d5934b",
"assets/shaders/ink_sparkle.frag": "ecc85a2e95f5e9f53123dcaf8cb9b6ce",
"canvaskit/canvaskit.js": "86e461cf471c1640fd2b461ece4589df",
"canvaskit/canvaskit.js.symbols": "68eb703b9a609baef8ee0e413b442f33",
"canvaskit/canvaskit.wasm": "efeeba7dcc952dae57870d4df3111fad",
"canvaskit/chromium/canvaskit.js": "34beda9f39eb7d992d46125ca868dc61",
"canvaskit/chromium/canvaskit.js.symbols": "5a23598a2a8efd18ec3b60de5d28af8f",
"canvaskit/chromium/canvaskit.wasm": "64a386c87532ae52ae041d18a32a3635",
"canvaskit/skwasm.js": "f2ad9363618c5f62e813740099a80e63",
"canvaskit/skwasm.js.symbols": "80806576fa1056b43dd6d0b445b4b6f7",
"canvaskit/skwasm.wasm": "f0dfd99007f989368db17c9abeed5a49",
"canvaskit/skwasm_st.js": "d1326ceef381ad382ab492ba5d96f04d",
"canvaskit/skwasm_st.js.symbols": "c7e7aac7cd8b612defd62b43e3050bdd",
"canvaskit/skwasm_st.wasm": "56c3973560dfcbf28ce47cebe40f3206",
"favicon.png": "5dcef449791fa27946b3d35ad8803796",
"flutter.js": "76f08d47ff9f5715220992f993002504",
"flutter_bootstrap.js": "8f95e5c250bbe65b57b68cd4252a5a65",
"icons/Icon-192.png": "ac9a721a12bbc803b44f645561ecb1e1",
"icons/Icon-512.png": "96e752610906ba2a93c65f8abe1645f1",
"icons/Icon-maskable-192.png": "c457ef57daa1d16f64b27b786ec2ea3c",
"icons/Icon-maskable-512.png": "301a7604d45b3e739efc881eb04896ea",
"index.html": "9f40cb15019a2babe778b4a3fefe0864",
"/": "9f40cb15019a2babe778b4a3fefe0864",
"main.dart.js": "fc7b72c7c6898b8afe2be36e061eadfa",
"manifest.json": "f0c66419b904eee7746b9c8430d99f8f",
"version.json": "d207f01e03038d4e323b85f0cb87471d"};
// The application shell files that are downloaded before a service worker can
// start.
const CORE = ["main.dart.js",
"index.html",
"flutter_bootstrap.js",
"assets/AssetManifest.bin.json",
"assets/FontManifest.json"];

// During install, the TEMP cache is populated with the application shell files.
self.addEventListener("install", (event) => {
  self.skipWaiting();
  return event.waitUntil(
    caches.open(TEMP).then((cache) => {
      return cache.addAll(
        CORE.map((value) => new Request(value, {'cache': 'reload'})));
    })
  );
});
// During activate, the cache is populated with the temp files downloaded in
// install. If this service worker is upgrading from one with a saved
// MANIFEST, then use this to retain unchanged resource files.
self.addEventListener("activate", function(event) {
  return event.waitUntil(async function() {
    try {
      var contentCache = await caches.open(CACHE_NAME);
      var tempCache = await caches.open(TEMP);
      var manifestCache = await caches.open(MANIFEST);
      var manifest = await manifestCache.match('manifest');
      // When there is no prior manifest, clear the entire cache.
      if (!manifest) {
        await caches.delete(CACHE_NAME);
        contentCache = await caches.open(CACHE_NAME);
        for (var request of await tempCache.keys()) {
          var response = await tempCache.match(request);
          await contentCache.put(request, response);
        }
        await caches.delete(TEMP);
        // Save the manifest to make future upgrades efficient.
        await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
        // Claim client to enable caching on first launch
        self.clients.claim();
        return;
      }
      var oldManifest = await manifest.json();
      var origin = self.location.origin;
      for (var request of await contentCache.keys()) {
        var key = request.url.substring(origin.length + 1);
        if (key == "") {
          key = "/";
        }
        // If a resource from the old manifest is not in the new cache, or if
        // the MD5 sum has changed, delete it. Otherwise the resource is left
        // in the cache and can be reused by the new service worker.
        if (!RESOURCES[key] || RESOURCES[key] != oldManifest[key]) {
          await contentCache.delete(request);
        }
      }
      // Populate the cache with the app shell TEMP files, potentially overwriting
      // cache files preserved above.
      for (var request of await tempCache.keys()) {
        var response = await tempCache.match(request);
        await contentCache.put(request, response);
      }
      await caches.delete(TEMP);
      // Save the manifest to make future upgrades efficient.
      await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
      // Claim client to enable caching on first launch
      self.clients.claim();
      return;
    } catch (err) {
      // On an unhandled exception the state of the cache cannot be guaranteed.
      console.error('Failed to upgrade service worker: ' + err);
      await caches.delete(CACHE_NAME);
      await caches.delete(TEMP);
      await caches.delete(MANIFEST);
    }
  }());
});
// The fetch handler redirects requests for RESOURCE files to the service
// worker cache.
self.addEventListener("fetch", (event) => {
  if (event.request.method !== 'GET') {
    return;
  }
  var origin = self.location.origin;
  var key = event.request.url.substring(origin.length + 1);
  // Redirect URLs to the index.html
  if (key.indexOf('?v=') != -1) {
    key = key.split('?v=')[0];
  }
  if (event.request.url == origin || event.request.url.startsWith(origin + '/#') || key == '') {
    key = '/';
  }
  // If the URL is not the RESOURCE list then return to signal that the
  // browser should take over.
  if (!RESOURCES[key]) {
    return;
  }
  // If the URL is the index.html, perform an online-first request.
  if (key == '/') {
    return onlineFirst(event);
  }
  event.respondWith(caches.open(CACHE_NAME)
    .then((cache) =>  {
      return cache.match(event.request).then((response) => {
        // Either respond with the cached resource, or perform a fetch and
        // lazily populate the cache only if the resource was successfully fetched.
        return response || fetch(event.request).then((response) => {
          if (response && Boolean(response.ok)) {
            cache.put(event.request, response.clone());
          }
          return response;
        });
      })
    })
  );
});
self.addEventListener('message', (event) => {
  // SkipWaiting can be used to immediately activate a waiting service worker.
  // This will also require a page refresh triggered by the main worker.
  if (event.data === 'skipWaiting') {
    self.skipWaiting();
    return;
  }
  if (event.data === 'downloadOffline') {
    downloadOffline();
    return;
  }
});
// Download offline will check the RESOURCES for all files not in the cache
// and populate them.
async function downloadOffline() {
  var resources = [];
  var contentCache = await caches.open(CACHE_NAME);
  var currentContent = {};
  for (var request of await contentCache.keys()) {
    var key = request.url.substring(origin.length + 1);
    if (key == "") {
      key = "/";
    }
    currentContent[key] = true;
  }
  for (var resourceKey of Object.keys(RESOURCES)) {
    if (!currentContent[resourceKey]) {
      resources.push(resourceKey);
    }
  }
  return contentCache.addAll(resources);
}
// Attempt to download the resource online before falling back to
// the offline cache.
function onlineFirst(event) {
  return event.respondWith(
    fetch(event.request).then((response) => {
      return caches.open(CACHE_NAME).then((cache) => {
        cache.put(event.request, response.clone());
        return response;
      });
    }).catch((error) => {
      return caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((response) => {
          if (response != null) {
            return response;
          }
          throw error;
        });
      });
    })
  );
}
