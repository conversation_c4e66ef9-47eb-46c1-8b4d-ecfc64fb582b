import 'package:flutter/material.dart';
import 'package:godavari_pushkaralu_app/constants/theme.dart';
import 'package:lottie/lottie.dart';

class PremiumScreen extends StatefulWidget {
  const PremiumScreen({super.key});

  @override
  State<PremiumScreen> createState() => _PremiumScreenState();
}

class _PremiumScreenState extends State<PremiumScreen> {
  bool _loading = true;
  bool _showSuccess = false;

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) setState(() => _loading = false);
    });
  }

  void _simulateSubscribe() async {
    setState(() => _loading = true);
    await Future.delayed(const Duration(seconds: 1));
    setState(() {
      _loading = false;
      _showSuccess = true;
    });
    await Future.delayed(const Duration(seconds: 2));
    if (mounted) setState(() => _showSuccess = false);
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return Scaffold(
        body: Center(
          child: Lottie.asset('assets/lottie/loading.json', width: 120, repeat: true),
        ),
      );
    }
    if (_showSuccess) {
      return Scaffold(
        body: Center(
          child: Lottie.asset('assets/lottie/success.json', width: 120, repeat: false),
        ),
      );
    }
    return _buildPremium(context);
  }

  Widget _buildPremium(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Premium Services'),
        backgroundColor: const Color(0xFFFFD700),
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: ListView(
        padding: const EdgeInsets.all(24),
        children: [
          Center(
            child: Text('Choose Your Premium Tier',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)),
          ),
          const SizedBox(height: 24),
          _buildPremiumTier('Gold', 'All-access, VIP darshan, luxury stay', 'assets/icons/placeholder_gold_badge.png', Colors.amber, true),
          const SizedBox(height: 16),
          _buildPremiumTier('Silver', 'Priority access, special sevas', 'assets/icons/placeholder_silver_badge.png', Colors.grey, false),
          const SizedBox(height: 16),
          _buildPremiumTier('Bronze', 'Basic premium features', 'assets/icons/placeholder_bronze_badge.png', Colors.brown, false),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: _simulateSubscribe,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              foregroundColor: Colors.black,
              minimumSize: const Size.fromHeight(48),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              elevation: 6,
            ),
            child: const Text('Subscribe', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
          ),
        ],
      ),
    );
  }

  Widget _buildPremiumTier(String title, String desc, String icon, Color color, bool selected) {
    return Container(
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
        color: selected ? color.withOpacity(0.15) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color, width: selected ? 2 : 1),
        boxShadow: [AppTheme.softShadow],
      ),
      child: Row(
        children: [
          Image.asset(icon, height: 40, width: 40),
          const SizedBox(width: 18),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: TextStyle(fontWeight: FontWeight.bold, color: color, fontSize: 18)),
                const SizedBox(height: 4),
                Text(desc, style: const TextStyle(color: Colors.black87)),
              ],
            ),
          ),
          if (selected)
            const Icon(Icons.check_circle, color: Colors.amber, size: 28),
        ],
      ),
    );
  }
} 