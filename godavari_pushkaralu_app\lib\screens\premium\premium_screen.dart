import 'package:flutter/material.dart';
import 'package:godavari_pushkaralu_app/constants/theme.dart';

class PremiumScreen extends StatelessWidget {
  const PremiumScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Premium Services'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            _buildPremiumTiers(context),
            const SizedBox(height: 24),
            _buildFeaturesComparison(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: <PERSON>umn(
        children: [
          const Icon(
            Icons.star,
            color: Colors.white,
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'Upgrade Your Experience',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose your perfect Pushkaralu experience',
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPremiumTiers(BuildContext context) {
    return Column(
      children: [
        _buildPremiumCard(
          context,
          '🥉',
          'Bronze Fast Track',
          '₹999',
          [
            'Skip general queues at all ghats',
            'Priority parking reservations',
            'SMS alerts for crowd levels',
            '24/7 helpline support',
            'Advanced accommodation booking',
            'Digital darshan tickets',
            'Exclusive bronze member rest areas',
          ],
          AppTheme.primaryColor,
        ),
        const SizedBox(height: 16),
        _buildPremiumCard(
          context,
          '🥈',
          'Silver VIP Experience',
          '₹2,499',
          [
            'All Bronze features PLUS:',
            'VIP ghat access sections',
            'Personal AI pilgrimage guide',
            'Helicopter tour booking',
            'Premium AC bus services',
            'Live streaming access',
            'Complimentary prasadam daily',
          ],
          AppTheme.secondaryColor,
        ),
        const SizedBox(height: 16),
        _buildPremiumCard(
          context,
          '🥇',
          'Gold Royal Pilgrimage',
          '₹4,999',
          [
            'All Silver features PLUS:',
            'Private boat rides on Godavari',
            'Personal priest services',
            'Luxury hotel accommodations',
            'Airport transfer services',
            'VIP darshan (skip all queues)',
            'Professional photography',
            '24/7 personal assistant',
          ],
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildPremiumCard(
    BuildContext context,
    String emoji,
    String title,
    String price,
    List<String> features,
    Color color,
  ) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: color.withOpacity(0.3), width: 2),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  emoji,
                  style: const TextStyle(fontSize: 32),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      Text(
                        price,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ...features.map((feature) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.check_circle,
                    color: color,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      feature,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            )),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // TODO: Implement subscription logic
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: color,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Subscribe Now',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesComparison() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Why Choose Premium?',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 16),
            _buildFeatureRow(
              Icons.timer,
              'Skip Long Queues',
              'Save hours of waiting time',
            ),
            _buildFeatureRow(
              Icons.security,
              'Priority Access',
              'Exclusive entry to all facilities',
            ),
            _buildFeatureRow(
              Icons.support_agent,
              '24/7 Support',
              'Round-the-clock assistance',
            ),
            _buildFeatureRow(
              Icons.hotel,
              'Premium Accommodation',
              'Luxury stays near ghats',
            ),
            _buildFeatureRow(
              Icons.directions_bus,
              'VIP Transportation',
              'Comfortable travel between ghats',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureRow(IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 