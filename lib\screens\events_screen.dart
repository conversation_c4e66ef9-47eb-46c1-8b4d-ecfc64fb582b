import 'package:flutter/material.dart';

class EventsScreen extends StatelessWidget {
  const EventsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Events Information'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: <PERSON>um<PERSON>(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current Events
            const Text(
              'Today\'s Events',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            
            const SizedBox(height: 16),
            
            _buildEventCard(
              'Morning Aarti',
              '5:00 AM - 6:00 AM',
              'Main Ghat',
              'Daily morning prayers and rituals',
              Icons.wb_sunny,
              Colors.orange,
              true,
            ),
            
            _buildEventCard(
              '<PERSON><PERSON><PERSON> Snanam',
              '6:00 AM - 12:00 PM',
              'All Ghats',
              'Sacred bathing ceremony',
              Icons.water,
              Colors.blue,
              true,
            ),
            
            _buildEventCard(
              'Cultural Program',
              '7:00 PM - 9:00 PM',
              'Main Stage',
              'Traditional dance and music performances',
              Icons.music_note,
              Colors.purple,
              false,
            ),
            
            const SizedBox(height: 24),
            
            // Upcoming Events
            const Text(
              'Upcoming Events',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            
            const SizedBox(height: 16),
            
            _buildEventCard(
              'Special Abhishekam',
              'Tomorrow 6:00 AM',
              'Kaleshwaram Temple',
              'Special prayers for devotees',
              Icons.temple_hindu,
              Colors.red,
              false,
            ),
            
            _buildEventCard(
              'Spiritual Discourse',
              'Tomorrow 4:00 PM',
              'Discourse Hall',
              'Teachings by renowned spiritual leaders',
              Icons.book,
              Colors.green,
              false,
            ),
            
            _buildEventCard(
              'Boat Festival',
              'Day after tomorrow',
              'River Godavari',
              'Decorated boats with cultural programs',
              Icons.directions_boat,
              Colors.teal,
              false,
            ),
            
            const SizedBox(height: 24),
            
            // Event Categories
            const Text(
              'Event Categories',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            
            const SizedBox(height: 16),
            
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              childAspectRatio: 1.5,
              children: [
                _buildCategoryCard('Religious', Icons.temple_hindu, Colors.orange),
                _buildCategoryCard('Cultural', Icons.music_note, Colors.purple),
                _buildCategoryCard('Educational', Icons.school, Colors.blue),
                _buildCategoryCard('Community', Icons.groups, Colors.green),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEventCard(
    String title,
    String time,
    String location,
    String description,
    IconData icon,
    Color color,
    bool isLive,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            title,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          if (isLive) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'LIVE',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.access_time, size: 14, color: Colors.grey[600]),
                          const SizedBox(width: 4),
                          Text(
                            time,
                            style: TextStyle(color: Colors.grey[600], fontSize: 14),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              description,
              style: const TextStyle(fontSize: 14, color: Colors.black87),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.location_on, size: 14, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  location,
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Reminder set for $title')),
                    );
                  },
                  child: const Text('Set Reminder'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryCard(String title, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () {
          // Handle category selection
        },
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
