import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class StayFoodDetailedScreen extends StatefulWidget {
  const StayFoodDetailedScreen({super.key});

  @override
  State<StayFoodDetailedScreen> createState() => _StayFoodDetailedScreenState();
}

class _StayFoodDetailedScreenState extends State<StayFoodDetailedScreen> {
  String _selectedFilter = 'All';
  String _selectedSort = 'Price';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildAccommodationTypes(),
                  _buildFilters(),
                  _buildHotelListings(),
                  _buildAnnadanamSection(),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomNavBar(),
    );
  }

  Widget _buildHeader() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1976D2),
            Color(0xFF42A5F5),
            Color(0xFFFFB74D),
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              GestureDetector(
                onTap: () => context.pop(),
                child: const Icon(
                  Icons.arrow_back,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Text(
                  'Stay & Food',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.location_on,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 8),
              const Text(
                'Rajahmundry',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAccommodationTypes() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Accommodation Types',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildAccommodationCard(
                  icon: Icons.temple_hindu,
                  color: Colors.orange,
                  title: 'Dharmashalas',
                  subtitle: 'Free',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildAccommodationCard(
                  icon: Icons.home,
                  color: const Color(0xFF1976D2),
                  title: 'Guest Houses',
                  subtitle: 'Budget',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildAccommodationCard(
                  icon: Icons.hotel,
                  color: Colors.purple,
                  title: 'Hotels',
                  subtitle: 'Comfort',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildAccommodationCard(
                  icon: Icons.house,
                  color: const Color(0xFF4CAF50),
                  title: 'Homestays',
                  subtitle: 'Local',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAccommodationCard({
    required IconData icon,
    required Color color,
    required String title,
    required String subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          _buildFilterChip('Filters', Icons.filter_list),
          const SizedBox(width: 8),
          _buildFilterChip('Price', Icons.attach_money),
          const SizedBox(width: 8),
          _buildFilterChip('Distance', Icons.location_on),
          const SizedBox(width: 8),
          _buildFilterChip('Amenities', Icons.star),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Text(
              'Available',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: Colors.grey),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHotelListings() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Column(
        children: [
          _buildHotelCard(
            image: 'assets/images/sri_rama_dharamshala.jpg',
            name: 'Sri Rama Dharamshala',
            location: 'Pushkar Ghat Area',
            distance: '0.2 km from main ghat',
            rating: 4.5,
            reviews: 120,
            price: 'Free',
            priceSubtitle: 'Per night',
            amenities: [Icons.wifi, Icons.restaurant],
            isPopular: false,
          ),
          const SizedBox(height: 16),
          _buildHotelCard(
            image: 'assets/images/godavari_guest_house.jpg',
            name: 'Godavari Guest House',
            location: 'Near Kotilingeshwara Temple',
            distance: '0.5 km from main ghat',
            rating: 4.2,
            reviews: 85,
            price: '₹800',
            priceSubtitle: 'Per night',
            amenities: [Icons.wifi, Icons.ac_unit, Icons.local_parking],
            isPopular: true,
          ),
          const SizedBox(height: 16),
          _buildHotelCard(
            image: 'assets/images/river_view_hotel.jpg',
            name: 'River View Hotel',
            location: 'Premium Location',
            distance: '0.1 km from main ghat',
            rating: 4.8,
            reviews: 220,
            price: '₹3,500',
            priceSubtitle: 'Per night',
            amenities: [Icons.wifi, Icons.ac_unit, Icons.pool, Icons.spa],
            isPopular: false,
          ),
        ],
      ),
    );
  }
