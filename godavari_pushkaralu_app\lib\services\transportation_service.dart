import 'dart:async';
import 'dart:math';
import 'package:geolocator/geolocator.dart';

enum TransportType { bus, train, taxi, autoRickshaw, boat, shuttle }
enum VehicleStatus { available, busy, maintenance, offline }
enum TrafficLevel { light, moderate, heavy, blocked }

class Vehicle {
  final String id;
  final String number;
  final TransportType type;
  final VehicleStatus status;
  final double latitude;
  final double longitude;
  final String driverName;
  final String driverPhone;
  final int capacity;
  final int currentPassengers;
  final double fare;
  final double rating;
  final DateTime lastUpdated;
  final String route;
  final DateTime? estimatedArrival;

  Vehicle({
    required this.id,
    required this.number,
    required this.type,
    required this.status,
    required this.latitude,
    required this.longitude,
    required this.driverName,
    required this.driverPhone,
    required this.capacity,
    required this.currentPassengers,
    required this.fare,
    required this.rating,
    required this.lastUpdated,
    required this.route,
    this.estimatedArrival,
  });

  Vehicle copyWith({
    VehicleStatus? status,
    double? latitude,
    double? longitude,
    int? currentPassengers,
    DateTime? lastUpdated,
    DateTime? estimatedArrival,
  }) {
    return Vehicle(
      id: id,
      number: number,
      type: type,
      status: status ?? this.status,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      driverName: driverName,
      driverPhone: driverPhone,
      capacity: capacity,
      currentPassengers: currentPassengers ?? this.currentPassengers,
      fare: fare,
      rating: rating,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      route: route,
      estimatedArrival: estimatedArrival ?? this.estimatedArrival,
    );
  }
}

class ParkingSpot {
  final String id;
  final String name;
  final double latitude;
  final double longitude;
  final int totalSpots;
  final int availableSpots;
  final double hourlyRate;
  final bool isOpen;
  final DateTime lastUpdated;
  final List<String> amenities;

  ParkingSpot({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.totalSpots,
    required this.availableSpots,
    required this.hourlyRate,
    required this.isOpen,
    required this.lastUpdated,
    required this.amenities,
  });

  ParkingSpot copyWith({
    int? availableSpots,
    bool? isOpen,
    DateTime? lastUpdated,
  }) {
    return ParkingSpot(
      id: id,
      name: name,
      latitude: latitude,
      longitude: longitude,
      totalSpots: totalSpots,
      availableSpots: availableSpots ?? this.availableSpots,
      hourlyRate: hourlyRate,
      isOpen: isOpen ?? this.isOpen,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      amenities: amenities,
    );
  }
}

class TrafficUpdate {
  final String routeId;
  final String routeName;
  final TrafficLevel trafficLevel;
  final String description;
  final DateTime timestamp;
  final int delayMinutes;

  TrafficUpdate({
    required this.routeId,
    required this.routeName,
    required this.trafficLevel,
    required this.description,
    required this.timestamp,
    required this.delayMinutes,
  });
}

class BookingRequest {
  final String vehicleId;
  final String pickupLocation;
  final String dropLocation;
  final DateTime requestTime;
  final int passengers;
  final String customerName;
  final String customerPhone;

  BookingRequest({
    required this.vehicleId,
    required this.pickupLocation,
    required this.dropLocation,
    required this.requestTime,
    required this.passengers,
    required this.customerName,
    required this.customerPhone,
  });
}

class TransportationService {
  static TransportationService? _instance;
  
  factory TransportationService() {
    return _instance ??= TransportationService._internal();
  }
  
  TransportationService._internal();
  
  // For testing - create a new instance
  TransportationService.newInstance();

  final StreamController<List<Vehicle>> _vehiclesController = 
      StreamController<List<Vehicle>>.broadcast();
  final StreamController<List<ParkingSpot>> _parkingController = 
      StreamController<List<ParkingSpot>>.broadcast();
  final StreamController<List<TrafficUpdate>> _trafficController = 
      StreamController<List<TrafficUpdate>>.broadcast();

  Stream<List<Vehicle>> get vehiclesStream => _vehiclesController.stream;
  Stream<List<ParkingSpot>> get parkingStream => _parkingController.stream;
  Stream<List<TrafficUpdate>> get trafficStream => _trafficController.stream;

  Timer? _updateTimer;
  List<Vehicle> _vehicles = [];
  List<ParkingSpot> _parkingSpots = [];
  List<TrafficUpdate> _trafficUpdates = [];
  Position? _currentPosition;

  void initialize() {
    _initializeVehicles();
    _initializeParkingSpots();
    _initializeTrafficUpdates();
    _startRealTimeUpdates();
    _getCurrentLocation();
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) return;

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) return;
      }

      _currentPosition = await Geolocator.getCurrentPosition();
    } catch (e) {
      // Handle location error
    }
  }

  void _initializeVehicles() {
    _vehicles = [
      // Buses
      Vehicle(
        id: 'bus_001',
        number: 'AP 28 Z 1234',
        type: TransportType.bus,
        status: VehicleStatus.available,
        latitude: 17.0081,
        longitude: 81.7734,
        driverName: 'Ravi Kumar',
        driverPhone: '+91-9876543210',
        capacity: 45,
        currentPassengers: 12,
        fare: 25.0,
        rating: 4.2,
        lastUpdated: DateTime.now(),
        route: 'Rajahmundry - Pushkar Ghat',
        estimatedArrival: DateTime.now().add(const Duration(minutes: 8)),
      ),
      Vehicle(
        id: 'bus_002',
        number: 'AP 28 Z 5678',
        type: TransportType.bus,
        status: VehicleStatus.busy,
        latitude: 17.0095,
        longitude: 81.7720,
        driverName: 'Suresh Babu',
        driverPhone: '+91-9876543211',
        capacity: 45,
        currentPassengers: 38,
        fare: 25.0,
        rating: 4.5,
        lastUpdated: DateTime.now(),
        route: 'Railway Station - Pushkar Ghat',
        estimatedArrival: DateTime.now().add(const Duration(minutes: 15)),
      ),

      // Taxis
      Vehicle(
        id: 'taxi_001',
        number: 'AP 28 C 9876',
        type: TransportType.taxi,
        status: VehicleStatus.available,
        latitude: 17.0075,
        longitude: 81.7740,
        driverName: 'Venkat Reddy',
        driverPhone: '+91-9876543212',
        capacity: 4,
        currentPassengers: 0,
        fare: 150.0,
        rating: 4.7,
        lastUpdated: DateTime.now(),
        route: 'City Center - Ghat Area',
        estimatedArrival: DateTime.now().add(const Duration(minutes: 5)),
      ),

      // Auto Rickshaws
      Vehicle(
        id: 'auto_001',
        number: 'AP 28 R 4567',
        type: TransportType.autoRickshaw,
        status: VehicleStatus.available,
        latitude: 17.0088,
        longitude: 81.7728,
        driverName: 'Ramesh',
        driverPhone: '+91-9876543213',
        capacity: 3,
        currentPassengers: 1,
        fare: 80.0,
        rating: 4.1,
        lastUpdated: DateTime.now(),
        route: 'Local Area',
        estimatedArrival: DateTime.now().add(const Duration(minutes: 3)),
      ),

      // Boats
      Vehicle(
        id: 'boat_001',
        number: 'GP-BOAT-01',
        type: TransportType.boat,
        status: VehicleStatus.available,
        latitude: 17.0082,
        longitude: 81.7735,
        driverName: 'Fisherman Raju',
        driverPhone: '+91-9876543214',
        capacity: 20,
        currentPassengers: 8,
        fare: 50.0,
        rating: 4.3,
        lastUpdated: DateTime.now(),
        route: 'Ghat Crossing',
        estimatedArrival: DateTime.now().add(const Duration(minutes: 2)),
      ),

      // Shuttle Services
      Vehicle(
        id: 'shuttle_001',
        number: 'GP-SHUTTLE-01',
        type: TransportType.shuttle,
        status: VehicleStatus.available,
        latitude: 17.0070,
        longitude: 81.7750,
        driverName: 'Govind',
        driverPhone: '+91-9876543215',
        capacity: 25,
        currentPassengers: 15,
        fare: 20.0,
        rating: 4.4,
        lastUpdated: DateTime.now(),
        route: 'Parking - Main Ghat',
        estimatedArrival: DateTime.now().add(const Duration(minutes: 6)),
      ),
    ];

    _vehiclesController.add(_vehicles);
  }

  void _initializeParkingSpots() {
    _parkingSpots = [
      ParkingSpot(
        id: 'parking_001',
        name: 'Main Ghat Parking',
        latitude: 17.0085,
        longitude: 81.7730,
        totalSpots: 200,
        availableSpots: 45,
        hourlyRate: 20.0,
        isOpen: true,
        lastUpdated: DateTime.now(),
        amenities: ['Security', 'CCTV', 'Washrooms', 'Water'],
      ),
      ParkingSpot(
        id: 'parking_002',
        name: 'Secondary Parking Area',
        latitude: 17.0070,
        longitude: 81.7745,
        totalSpots: 150,
        availableSpots: 78,
        hourlyRate: 15.0,
        isOpen: true,
        lastUpdated: DateTime.now(),
        amenities: ['Security', 'Washrooms'],
      ),
      ParkingSpot(
        id: 'parking_003',
        name: 'VIP Parking',
        latitude: 17.0090,
        longitude: 81.7725,
        totalSpots: 50,
        availableSpots: 12,
        hourlyRate: 50.0,
        isOpen: true,
        lastUpdated: DateTime.now(),
        amenities: ['Valet Service', 'Security', 'CCTV', 'Washrooms', 'Refreshments'],
      ),
      ParkingSpot(
        id: 'parking_004',
        name: 'Bus Parking Terminal',
        latitude: 17.0065,
        longitude: 81.7755,
        totalSpots: 80,
        availableSpots: 23,
        hourlyRate: 30.0,
        isOpen: true,
        lastUpdated: DateTime.now(),
        amenities: ['Security', 'Driver Rest Area', 'Fuel Station'],
      ),
    ];

    _parkingController.add(_parkingSpots);
  }

  void _initializeTrafficUpdates() {
    _trafficUpdates = [
      TrafficUpdate(
        routeId: 'route_001',
        routeName: 'NH-16 to Pushkar Ghat',
        trafficLevel: TrafficLevel.heavy,
        description: 'Heavy traffic due to festival rush',
        timestamp: DateTime.now(),
        delayMinutes: 25,
      ),
      TrafficUpdate(
        routeId: 'route_002',
        routeName: 'Railway Station Road',
        trafficLevel: TrafficLevel.moderate,
        description: 'Moderate traffic, expect delays',
        timestamp: DateTime.now(),
        delayMinutes: 10,
      ),
      TrafficUpdate(
        routeId: 'route_003',
        routeName: 'Ghat Access Road',
        trafficLevel: TrafficLevel.light,
        description: 'Light traffic, smooth flow',
        timestamp: DateTime.now(),
        delayMinutes: 0,
      ),
      TrafficUpdate(
        routeId: 'route_004',
        routeName: 'Bypass Road',
        trafficLevel: TrafficLevel.blocked,
        description: 'Road blocked due to procession',
        timestamp: DateTime.now(),
        delayMinutes: 60,
      ),
    ];

    _trafficController.add(_trafficUpdates);
  }

  void _startRealTimeUpdates() {
    _updateTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      _updateVehiclePositions();
      _updateParkingAvailability();
      _updateTrafficConditions();
    });
  }

  void _updateVehiclePositions() {
    final random = Random();

    _vehicles = _vehicles.map((vehicle) {
      // Simulate vehicle movement
      double newLat = vehicle.latitude + (random.nextDouble() - 0.5) * 0.001;
      double newLng = vehicle.longitude + (random.nextDouble() - 0.5) * 0.001;

      // Simulate passenger changes
      int newPassengers = vehicle.currentPassengers;
      if (random.nextDouble() < 0.3) {
        if (vehicle.type == TransportType.bus || vehicle.type == TransportType.shuttle) {
          newPassengers = (newPassengers + random.nextInt(5) - 2).clamp(0, vehicle.capacity);
        } else {
          newPassengers = random.nextInt(vehicle.capacity + 1);
        }
      }

      // Update status based on passengers
      VehicleStatus newStatus = vehicle.status;
      if (newPassengers >= vehicle.capacity) {
        newStatus = VehicleStatus.busy;
      } else if (newPassengers == 0 && random.nextDouble() < 0.1) {
        newStatus = VehicleStatus.available;
      }

      // Update estimated arrival
      DateTime? newArrival;
      if (vehicle.estimatedArrival != null) {
        newArrival = vehicle.estimatedArrival!.subtract(const Duration(seconds: 15));
        if (newArrival.isBefore(DateTime.now())) {
          newArrival = DateTime.now().add(Duration(minutes: random.nextInt(20) + 5));
        }
      }

      return vehicle.copyWith(
        latitude: newLat,
        longitude: newLng,
        currentPassengers: newPassengers,
        status: newStatus,
        lastUpdated: DateTime.now(),
        estimatedArrival: newArrival,
      );
    }).toList();

    _vehiclesController.add(_vehicles);
  }

  void _updateParkingAvailability() {
    final random = Random();

    _parkingSpots = _parkingSpots.map((spot) {
      // Simulate parking changes
      int change = random.nextInt(10) - 5; // -5 to +4
      int newAvailable = (spot.availableSpots + change).clamp(0, spot.totalSpots);

      return spot.copyWith(
        availableSpots: newAvailable,
        lastUpdated: DateTime.now(),
      );
    }).toList();

    _parkingController.add(_parkingSpots);
  }

  void _updateTrafficConditions() {
    final random = Random();

    _trafficUpdates = _trafficUpdates.map((update) {
      // Simulate traffic changes
      TrafficLevel newLevel = update.trafficLevel;
      int newDelay = update.delayMinutes;

      if (random.nextDouble() < 0.2) {
        switch (update.trafficLevel) {
          case TrafficLevel.light:
            newLevel = random.nextBool() ? TrafficLevel.moderate : TrafficLevel.light;
            newDelay = newLevel == TrafficLevel.moderate ? 10 : 0;
            break;
          case TrafficLevel.moderate:
            newLevel = TrafficLevel.values[random.nextInt(3)]; // light, moderate, heavy
            newDelay = [0, 10, 25][TrafficLevel.values.indexOf(newLevel)];
            break;
          case TrafficLevel.heavy:
            newLevel = random.nextBool() ? TrafficLevel.moderate : TrafficLevel.heavy;
            newDelay = newLevel == TrafficLevel.moderate ? 10 : 25;
            break;
          case TrafficLevel.blocked:
            newLevel = random.nextDouble() < 0.1 ? TrafficLevel.heavy : TrafficLevel.blocked;
            newDelay = newLevel == TrafficLevel.heavy ? 25 : 60;
            break;
        }
      }

      return TrafficUpdate(
        routeId: update.routeId,
        routeName: update.routeName,
        trafficLevel: newLevel,
        description: _getTrafficDescription(newLevel),
        timestamp: DateTime.now(),
        delayMinutes: newDelay,
      );
    }).toList();

    _trafficController.add(_trafficUpdates);
  }

  String _getTrafficDescription(TrafficLevel level) {
    switch (level) {
      case TrafficLevel.light:
        return 'Light traffic, smooth flow';
      case TrafficLevel.moderate:
        return 'Moderate traffic, expect delays';
      case TrafficLevel.heavy:
        return 'Heavy traffic due to festival rush';
      case TrafficLevel.blocked:
        return 'Road blocked due to procession';
    }
  }

  // Public methods
  List<Vehicle> get vehicles => List.from(_vehicles);
  List<ParkingSpot> get parkingSpots => List.from(_parkingSpots);
  List<TrafficUpdate> get trafficUpdates => List.from(_trafficUpdates);

  List<Vehicle> getVehiclesByType(TransportType type) {
    return _vehicles.where((vehicle) => vehicle.type == type).toList();
  }

  List<Vehicle> getAvailableVehicles() {
    return _vehicles.where((vehicle) => vehicle.status == VehicleStatus.available).toList();
  }

  List<ParkingSpot> getAvailableParking() {
    return _parkingSpots.where((spot) => spot.availableSpots > 0 && spot.isOpen).toList();
  }

  Future<String> bookVehicle(BookingRequest request) async {
    // Simulate booking process
    await Future.delayed(const Duration(seconds: 1));

    final vehicle = _vehicles.firstWhere((v) => v.id == request.vehicleId);
    if (vehicle.status == VehicleStatus.available &&
        vehicle.currentPassengers + request.passengers <= vehicle.capacity) {

      final updatedVehicle = vehicle.copyWith(
        currentPassengers: vehicle.currentPassengers + request.passengers,
        status: vehicle.currentPassengers + request.passengers >= vehicle.capacity
            ? VehicleStatus.busy
            : VehicleStatus.available,
        lastUpdated: DateTime.now(),
      );

      final index = _vehicles.indexWhere((v) => v.id == request.vehicleId);
      _vehicles[index] = updatedVehicle;
      _vehiclesController.add(_vehicles);

      return 'BK${DateTime.now().millisecondsSinceEpoch}';
    } else {
      throw Exception('Vehicle not available or insufficient capacity');
    }
  }

  void dispose() {
    _updateTimer?.cancel();
    _vehiclesController.close();
    _parkingController.close();
    _trafficController.close();
  }
}
