import 'dart:async';
import 'dart:math';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';

enum EmergencyType { medical, police, fire, disaster, security, lost }

enum EmergencyStatus { active, resolved, dispatched, pending }

class EmergencyContact {
  final String id;
  final String name;
  final String number;
  final EmergencyType type;
  final String location;
  final double latitude;
  final double longitude;
  final bool isAvailable;
  final int responseTimeMinutes;
  final String specialization;

  EmergencyContact({
    required this.id,
    required this.name,
    required this.number,
    required this.type,
    required this.location,
    required this.latitude,
    required this.longitude,
    required this.isAvailable,
    required this.responseTimeMinutes,
    required this.specialization,
  });

  EmergencyContact copyWith({
    bool? isAvailable,
    int? responseTimeMinutes,
  }) {
    return EmergencyContact(
      id: id,
      name: name,
      number: number,
      type: type,
      location: location,
      latitude: latitude,
      longitude: longitude,
      isAvailable: isAvailable ?? this.isAvailable,
      responseTimeMinutes: responseTimeMinutes ?? this.responseTimeMinutes,
      specialization: specialization,
    );
  }
}

class EmergencyIncident {
  final String id;
  final EmergencyType type;
  final String description;
  final double latitude;
  final double longitude;
  final DateTime timestamp;
  final EmergencyStatus status;
  final String reportedBy;
  final int priority; // 1-5, 1 being highest

  EmergencyIncident({
    required this.id,
    required this.type,
    required this.description,
    required this.latitude,
    required this.longitude,
    required this.timestamp,
    required this.status,
    required this.reportedBy,
    required this.priority,
  });
}

class EmergencyService {
  static final EmergencyService _instance = EmergencyService._internal();
  factory EmergencyService() => _instance;
  EmergencyService._internal();

  final StreamController<List<EmergencyContact>> _contactsController = 
      StreamController<List<EmergencyContact>>.broadcast();
  final StreamController<List<EmergencyIncident>> _incidentsController = 
      StreamController<List<EmergencyIncident>>.broadcast();
  
  Stream<List<EmergencyContact>> get contactsStream => _contactsController.stream;
  Stream<List<EmergencyIncident>> get incidentsStream => _incidentsController.stream;
  
  Timer? _updateTimer;
  List<EmergencyContact> _contacts = [];
  List<EmergencyIncident> _incidents = [];
  Position? _currentPosition;

  void initialize() {
    _initializeEmergencyContacts();
    _initializeIncidents();
    _startRealTimeUpdates();
    _getCurrentLocation();
  }

  void _initializeEmergencyContacts() {
    _contacts = [
      // Medical Emergency Contacts
      EmergencyContact(
        id: 'medical_108',
        name: 'Ambulance Service 108',
        number: '108',
        type: EmergencyType.medical,
        location: 'Rajahmundry Government Hospital',
        latitude: 17.0081,
        longitude: 81.7734,
        isAvailable: true,
        responseTimeMinutes: 8,
        specialization: 'Emergency Medical Services',
      ),
      EmergencyContact(
        id: 'medical_rajah',
        name: 'Rajahmundry District Hospital',
        number: '+91-883-2420001',
        type: EmergencyType.medical,
        location: 'Rajahmundry',
        latitude: 17.0005,
        longitude: 81.7880,
        isAvailable: true,
        responseTimeMinutes: 12,
        specialization: 'General Emergency',
      ),
      EmergencyContact(
        id: 'medical_bhadra',
        name: 'Bhadrachalam Hospital',
        number: '+91-8744-222222',
        type: EmergencyType.medical,
        location: 'Bhadrachalam',
        latitude: 17.6688,
        longitude: 80.8936,
        isAvailable: true,
        responseTimeMinutes: 15,
        specialization: 'Rural Emergency Care',
      ),

      // Police Emergency Contacts
      EmergencyContact(
        id: 'police_100',
        name: 'Police Emergency 100',
        number: '100',
        type: EmergencyType.police,
        location: 'All Locations',
        latitude: 17.0081,
        longitude: 81.7734,
        isAvailable: true,
        responseTimeMinutes: 5,
        specialization: 'Emergency Response',
      ),
      EmergencyContact(
        id: 'police_rajah',
        name: 'Rajahmundry Police Station',
        number: '+91-883-2420100',
        type: EmergencyType.police,
        location: 'Rajahmundry',
        latitude: 17.0081,
        longitude: 81.7734,
        isAvailable: true,
        responseTimeMinutes: 7,
        specialization: 'Local Law Enforcement',
      ),
      EmergencyContact(
        id: 'police_tourist',
        name: 'Tourist Police Helpline',
        number: '+91-883-2420200',
        type: EmergencyType.police,
        location: 'Tourist Areas',
        latitude: 17.0081,
        longitude: 81.7734,
        isAvailable: true,
        responseTimeMinutes: 10,
        specialization: 'Tourist Safety',
      ),

      // Fire Emergency
      EmergencyContact(
        id: 'fire_101',
        name: 'Fire Emergency 101',
        number: '101',
        type: EmergencyType.fire,
        location: 'All Locations',
        latitude: 17.0081,
        longitude: 81.7734,
        isAvailable: true,
        responseTimeMinutes: 10,
        specialization: 'Fire & Rescue',
      ),
      EmergencyContact(
        id: 'fire_rajah',
        name: 'Rajahmundry Fire Station',
        number: '+91-883-2420101',
        type: EmergencyType.fire,
        location: 'Rajahmundry',
        latitude: 17.0081,
        longitude: 81.7734,
        isAvailable: true,
        responseTimeMinutes: 12,
        specialization: 'Fire Fighting',
      ),

      // Disaster Management
      EmergencyContact(
        id: 'disaster_control',
        name: 'Disaster Control Room',
        number: '+91-883-2420300',
        type: EmergencyType.disaster,
        location: 'District Collectorate',
        latitude: 17.0081,
        longitude: 81.7734,
        isAvailable: true,
        responseTimeMinutes: 15,
        specialization: 'Disaster Management',
      ),

      // Security
      EmergencyContact(
        id: 'security_pushkaralu',
        name: 'Pushkaralu Security Control',
        number: '+91-883-2420400',
        type: EmergencyType.security,
        location: 'Main Control Room',
        latitude: 17.0081,
        longitude: 81.7734,
        isAvailable: true,
        responseTimeMinutes: 3,
        specialization: 'Event Security',
      ),

      // Lost & Found
      EmergencyContact(
        id: 'lost_found',
        name: 'Lost & Found Helpline',
        number: '+91-883-2420500',
        type: EmergencyType.lost,
        location: 'Information Centers',
        latitude: 17.0081,
        longitude: 81.7734,
        isAvailable: true,
        responseTimeMinutes: 5,
        specialization: 'Missing Persons',
      ),
    ];

    _contactsController.add(_contacts);
  }

  void _initializeIncidents() {
    final now = DateTime.now();
    _incidents = [
      EmergencyIncident(
        id: 'inc_001',
        type: EmergencyType.medical,
        description: 'Medical assistance required at Ghat 2',
        latitude: 17.0081,
        longitude: 81.7734,
        timestamp: now.subtract(const Duration(minutes: 5)),
        status: EmergencyStatus.dispatched,
        reportedBy: 'Volunteer Team',
        priority: 2,
      ),
      EmergencyIncident(
        id: 'inc_002',
        type: EmergencyType.security,
        description: 'Crowd control needed at main entrance',
        latitude: 17.0081,
        longitude: 81.7734,
        timestamp: now.subtract(const Duration(minutes: 12)),
        status: EmergencyStatus.active,
        reportedBy: 'Security Guard',
        priority: 3,
      ),
    ];

    _incidentsController.add(_incidents);
  }

  void _startRealTimeUpdates() {
    _updateTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      _updateEmergencyStatus();
    });
  }

  void _updateEmergencyStatus() {
    final random = Random();
    
    // Update contact availability and response times
    _contacts = _contacts.map((contact) {
      bool newAvailability = contact.isAvailable;
      int newResponseTime = contact.responseTimeMinutes;

      // Simulate availability changes
      if (random.nextDouble() < 0.1) {
        newAvailability = !contact.isAvailable;
      }

      // Simulate response time changes based on current incidents
      if (contact.isAvailable) {
        int activeIncidents = _incidents.where((inc) => 
          inc.status == EmergencyStatus.active && inc.type == contact.type).length;
        newResponseTime = contact.responseTimeMinutes + (activeIncidents * 2);
      }

      return contact.copyWith(
        isAvailable: newAvailability,
        responseTimeMinutes: newResponseTime,
      );
    }).toList();

    _contactsController.add(_contacts);
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) return;

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) return;
      }

      _currentPosition = await Geolocator.getCurrentPosition();
    } catch (e) {
      // Handle location error
    }
  }

  Future<bool> makeEmergencyCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    
    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<String> reportEmergency(EmergencyType type, String description) async {
    if (_currentPosition == null) {
      await _getCurrentLocation();
    }

    final incident = EmergencyIncident(
      id: 'inc_${DateTime.now().millisecondsSinceEpoch}',
      type: type,
      description: description,
      latitude: _currentPosition?.latitude ?? 17.0081,
      longitude: _currentPosition?.longitude ?? 81.7734,
      timestamp: DateTime.now(),
      status: EmergencyStatus.pending,
      reportedBy: 'Mobile App User',
      priority: type == EmergencyType.medical ? 1 : 2,
    );

    _incidents.insert(0, incident);
    _incidentsController.add(_incidents);

    return incident.id;
  }

  List<EmergencyContact> getNearestContacts(EmergencyType type) {
    if (_currentPosition == null) {
      return _contacts.where((c) => c.type == type).toList();
    }

    final typeContacts = _contacts.where((c) => c.type == type).toList();
    
    typeContacts.sort((a, b) {
      double distanceA = Geolocator.distanceBetween(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
        a.latitude,
        a.longitude,
      );
      double distanceB = Geolocator.distanceBetween(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
        b.latitude,
        b.longitude,
      );
      return distanceA.compareTo(distanceB);
    });

    return typeContacts;
  }

  List<EmergencyContact> get allContacts => List.from(_contacts);
  List<EmergencyIncident> get activeIncidents => List.from(_incidents);

  void dispose() {
    _updateTimer?.cancel();
    _contactsController.close();
    _incidentsController.close();
  }
}
